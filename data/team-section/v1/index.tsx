import { TeamSectionProps } from '@/src/sections/team/v1';
import {
  FaFacebookF,
  FaLinkedinIn,
  FaPinterest,
  FaTwitter,
} from 'react-icons/fa6';

export const teamSectionData: TeamSectionProps = {
  sectionHeading: {
    subtitle: 'our active members',
    title: 'Transforming Challenges into Seamless Solutions',
  },
  cards: [
    {
      slug: '#',
      image: {
        src: '/assets/images/team/team-1.png',
        alt: 'Sem Shafiq',
      },
      socials: [
        {
          href: 'https://www.facebook.com/',
          icon: <FaFacebookF />,
        },
        {
          href: 'https://twitter.com/',
          icon: <FaTwitter />,
        },
        {
          href: 'https://www.linkedin.com/',
          icon: <FaLinkedinIn />,
        },
        {
          href: 'https://www.pinterest.com/',
          icon: <FaPinterest />,
        },
      ],
      name: '<PERSON><PERSON>',
      about: 'CEO',
    },
    {
      slug: '#',
      image: {
        src: '/assets/images/team/team-2.png',
        alt: 'Madiha',
      },
      socials: [
        {
          href: 'https://www.facebook.com/',
          icon: <FaFacebookF />,
        },
        {
          href: 'https://twitter.com/',
          icon: <FaTwitter />,
        },
        {
          href: 'https://www.linkedin.com/',
          icon: <FaLinkedinIn />,
        },
        {
          href: 'https://www.pinterest.com/',
          icon: <FaPinterest />,
        },
      ],
      name: 'Madiha',
      about: 'Project Manager',
    },
    {
      slug: '#',
      image: {
        src: '/assets/images/team/people-3.png',
        alt: 'Jenny Wilson',
      },
      socials: [
        {
          href: 'https://www.facebook.com/',
          icon: <FaFacebookF />,
        },
        {
          href: 'https://twitter.com/',
          icon: <FaTwitter />,
        },
        {
          href: 'https://www.linkedin.com/',
          icon: <FaLinkedinIn />,
        },
        {
          href: 'https://www.pinterest.com/',
          icon: <FaPinterest />,
        },
      ],
      name: 'Jenny Wilson',
      about: 'Dog Trainer',
    },
    {
      slug: '#',
      image: {
        src: '/assets/images/team/people-1.png',
        alt: 'Brooklyn Simmons',
      },
      socials: [
        {
          href: 'https://www.facebook.com/',
          icon: <FaFacebookF />,
        },
        {
          href: 'https://twitter.com/',
          icon: <FaTwitter />,
        },
        {
          href: 'https://www.linkedin.com/',
          icon: <FaLinkedinIn />,
        },
        {
          href: 'https://www.pinterest.com/',
          icon: <FaPinterest />,
        },
      ],
      name: 'Brooklyn Simmons',
      about: 'President of Sales',
    },
    {
      slug: '#',
      image: {
        src: '/assets/images/team/people-2.png',
        alt: 'Ralph Edwards',
      },
      socials: [
        {
          href: 'https://www.facebook.com/',
          icon: <FaFacebookF />,
        },
        {
          href: 'https://twitter.com/',
          icon: <FaTwitter />,
        },
        {
          href: 'https://www.linkedin.com/',
          icon: <FaLinkedinIn />,
        },
        {
          href: 'https://www.pinterest.com/',
          icon: <FaPinterest />,
        },
      ],
      name: 'Ralph Edwards',
      about: 'Medical Assistant',
    },
    {
      slug: '#',
      image: {
        src: '/assets/images/team/people-3.png',
        alt: 'Jenny Wilson',
      },
      socials: [
        {
          href: 'https://www.facebook.com/',
          icon: <FaFacebookF />,
        },
        {
          href: 'https://twitter.com/',
          icon: <FaTwitter />,
        },
        {
          href: 'https://www.linkedin.com/',
          icon: <FaLinkedinIn />,
        },
        {
          href: 'https://www.pinterest.com/',
          icon: <FaPinterest />,
        },
      ],
      name: 'Jenny Wilson',
      about: 'Dog Trainer',
    },
  ],
};
