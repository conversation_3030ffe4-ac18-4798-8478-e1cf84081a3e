import { ProjectSectionProps } from '@/src/sections/project/v1';

export const projectSectionData: ProjectSectionProps = {
  sectionHeading: {
    subtitle: 'latest portfolio',
    title: 'Crafted Digital Solutions, Delivered',
  },
  isWave: false,
  works: [
    {
      slug: '/portfolio/invoicepoint',
      image: {
        src: '/assets/images/portfolio/invoicepoint.png',
        alt: 'Invoice Point App',
      },
      title: 'Invoice Point',
      description:
        'Invoice Point is a sleek app for quick and professional invoice generation.',
    },
    {
      slug: '/portfolio/dyzle',
      image: {
        src: '/assets/images/portfolio/dyzle.png',
        alt: 'Dyzle Temperatuurbewaking',
      },
      title: 'Dyzle Apps',
      description:
        'Dyzle Temperatuurbewaking, a digital sensor networking company.',
    },
    {
      slug: '/portfolio/ah',
      image: {
        src: '/assets/images/portfolio/ah.png',
        alt: '<PERSON>',
      },
      title: '<PERSON> App',
      description:
        'Streamlines the online shopping experience, making it easier for users to find deals and manage their orders.',
    },
    {
      slug: '/portfolio/eropuit',
      image: {
        src: '/assets/images/portfolio/eropuit.png',
        alt: 'ANWB Eropuit App',
      },
      title: 'ANWB Eropuit App',
      description:
        'Eropuit is a mobile app where ANWB users can find tailored bike and hike routes.',
    },
    {
      slug: '/portfolio/ing',
      image: {
        src: '/assets/images/portfolio/ing.png',
        alt: 'ING Banking App',
      },
      title: 'ING Banking App',
      description:
        'Allows users to effortlessly manage their finances, with key features like the iDeal Payment, Scanner and the Payment Request.',
    },
    {
      slug: '/portfolio/bosch',
      image: {
        src: '/assets/images/portfolio/bosch.png',
        alt: 'TNT Track & Trace',
      },
      title: 'Bosch Easy Control',
      description:
        'Together with Bosch Smart Radiator Thermostats, you can set independent temperatures in the different rooms in your home.',
    },
    {
      slug: '/portfolio/tnt',
      image: {
        src: '/assets/images/portfolio/tnt.png',
        alt: 'TNT Track & Trace',
      },
      title: 'TNT Track & Trace',
      description:
        'Track & Trace app where user can track packages, request quotes and manage their account.',
    },
    // {
    //   slug: '/portfolio/single',
    //   image: {
    //     src: '/assets/images/portfolio/8.png',
    //     alt: 'portfolio-1',
    //   },
    //   title: 'Cloud Migrate Pro',
    //   description: 'Lorem Ipsum is simply dummy',
    // },
  ],
};
