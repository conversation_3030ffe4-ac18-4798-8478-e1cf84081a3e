import { FAQSectionProps } from '@/src/sections/faq';

export const faqSectionData: FAQSectionProps = {
  sectionHeading: {
    subtitle: 'Our faq',
    title: 'Frequently asked question',
  },
  faqItems: [
    {
      question: 'What services do you offer?',
      answer:
        'Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loboAliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros rti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros ',
    },
    {
      question: 'How can IT solutions benefit my business?',
      answer:
        'Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loboAliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros rti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros ',
    },
    {
      question: 'What industries do you serve?',
      answer:
        'Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loboAliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros rti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros ',
    },
    {
      question: 'Are your services customizable?',
      answer:
        'Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loboAliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros rti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros ',
    },
    {
      question: 'How do you ensure data privacy in your solutions?',
      answer:
        'Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loboAliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros rti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros ',
    },
    {
      question:
        'Lorem Ipsum is simply ready to Ipsum is simplyprint print antype ?',
      answer:
        'Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loboAliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra .Aliquam eros justo, posuere lobortis  viverra laoreet augue mattis fmentum ullamcorper viverra laoreet Aliquam eros justo, posuere loborti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros rti viverra laoreet matti ullamcorper posuere viverra ullamcorper posuere viverra .Aliquam eros ',
    },
  ],
};
