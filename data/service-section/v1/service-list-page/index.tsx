import { ServiceSectionProps } from '@/src/sections/service/v1';
import { FaMobileAlt, FaLaptop } from 'react-icons/fa';
import { SiMaterialdesign } from 'react-icons/si';
import { GiArtificialHive } from 'react-icons/gi';
import { SiCrowdsource } from 'react-icons/si';
import { MdCloudCircle } from 'react-icons/md';
import { WiStars } from 'react-icons/wi';

function Icon1() {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 65 65"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M8.12799 55.3735C4.85988 51.6963 3.05469 46.9477 3.05469 42.0281C3.05469 37.1086 4.85988 32.36 8.12799 28.6828L30.1671 4.81564C30.3453 4.62883 30.5597 4.48012 30.7971 4.37852C31.0345 4.27693 31.2901 4.22454 31.5483 4.22454C31.8065 4.22454 32.0621 4.27693 32.2995 4.37852C32.5369 4.48012 32.7513 4.62883 32.9296 4.81564C33.3079 5.19622 33.5202 5.71104 33.5202 6.24767C33.5202 6.7843 33.3079 7.29912 32.9296 7.6797L10.9717 31.5063C8.39045 34.4072 6.9644 38.1552 6.9644 42.0383C6.9644 45.9214 8.39045 49.6693 10.9717 52.5703C12.1762 53.9262 13.6542 55.0115 15.3085 55.7547C16.9628 56.4979 18.7558 56.8822 20.5694 56.8822C22.383 56.8822 24.176 56.4979 25.8303 55.7547C27.4846 55.0115 28.9626 53.9262 30.1671 52.5703L55.3342 25.2906C57.0706 23.3294 58.0291 20.8006 58.0291 18.1813C58.0291 15.5619 57.0706 13.0331 55.3342 11.0719C54.5243 10.1617 53.531 9.43329 52.4194 8.93449C51.3079 8.43568 50.1034 8.17781 48.885 8.17781C47.6667 8.17781 46.4622 8.43568 45.3506 8.93449C44.2391 9.43329 43.2457 10.1617 42.4358 11.0719L17.2686 38.311C16.7466 38.8983 16.3539 39.5887 16.116 40.3376C15.878 41.0864 15.8001 41.8769 15.8874 42.6578C15.9382 43.4043 16.1453 44.1318 16.4952 44.7931C16.8451 45.4544 17.3301 46.0349 17.9186 46.4969C18.8575 47.1458 20.0069 47.4164 21.1367 47.2544C22.2665 47.0925 23.2935 46.51 24.0124 45.6234L30.1061 39.0016C30.2844 38.8148 30.4987 38.6661 30.7362 38.5645C30.9736 38.4629 31.2291 38.4105 31.4874 38.4105C31.7456 38.4105 32.0012 38.4629 32.2386 38.5645C32.476 38.6661 32.6903 38.8148 32.8686 39.0016C33.2469 39.3822 33.4593 39.897 33.4593 40.4336C33.4593 40.9702 33.2469 41.4851 32.8686 41.8656L26.7749 48.4875C25.4214 50.0643 23.5285 51.0797 21.4663 51.3351C19.404 51.5906 17.3206 51.0677 15.6233 49.8688C14.5592 49.0441 13.684 48.001 13.0569 46.8097C12.4297 45.6184 12.065 44.3066 11.9874 42.9625C11.8503 41.6186 11.9965 40.2609 12.4163 38.9769C12.8361 37.6929 13.5203 36.5112 14.4249 35.5078L39.5921 8.22814C40.7619 6.92121 42.1942 5.87573 43.7955 5.15996C45.3968 4.44418 47.131 4.07422 48.885 4.07422C50.639 4.07422 52.3733 4.44418 53.9746 5.15996C55.5759 5.87573 57.0082 6.92121 58.178 8.22814C60.6109 10.9648 61.9547 14.4992 61.9547 18.1609C61.9547 21.8227 60.6109 25.3571 58.178 28.0938L33.0108 55.3735C31.4464 57.1261 29.5295 58.5284 27.3854 59.4886C25.2414 60.4488 22.9186 60.9451 20.5694 60.9451C18.2202 60.9451 15.8974 60.4488 13.7534 59.4886C11.6093 58.5284 9.69234 57.1261 8.12799 55.3735Z" />
    </svg>
  );
}

function Icon2() {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 65 65"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M28.4375 62.9688C21.4341 62.9688 14.7176 60.1867 9.76546 55.2345C4.81333 50.2824 2.03125 43.5659 2.03125 36.5625C2.03125 29.5591 4.81333 22.8426 9.76546 17.8905C14.7176 12.9383 21.4341 10.1563 28.4375 10.1562C28.9762 10.1562 29.4929 10.3703 29.8738 10.7512C30.2547 11.1321 30.4688 11.6488 30.4688 12.1875V34.5312H52.8125C53.3512 34.5312 53.8679 34.7453 54.2488 35.1262C54.6297 35.5071 54.8438 36.0238 54.8438 36.5625C54.8438 43.5659 52.0617 50.2824 47.1095 55.2345C42.1574 60.1867 35.4409 62.9688 28.4375 62.9688ZM26.4062 14.2188C22.1882 14.6233 18.1722 16.219 14.8267 18.8195C11.4812 21.4201 8.94419 24.9184 7.51157 28.9062C6.07896 32.8941 5.80984 37.2071 6.73561 41.3421C7.66139 45.4771 9.74389 49.2636 12.7402 52.2598C15.7364 55.2561 19.5229 57.3386 23.6579 58.2644C27.7929 59.1902 32.1059 58.921 36.0938 57.4884C40.0816 56.0558 43.5799 53.5188 46.1805 50.1733C48.781 46.8278 50.3767 42.8118 50.7812 38.5938H28.4375C27.8988 38.5938 27.3821 38.3797 27.0012 37.9988C26.6203 37.6179 26.4062 37.1012 26.4062 36.5625V14.2188Z" />
      <path d="M60.9375 30.4688H36.5625C36.0238 30.4688 35.5071 30.2547 35.1262 29.8738C34.7453 29.4929 34.5312 28.9762 34.5312 28.4375V4.0625C34.5312 3.52378 34.7453 3.00712 35.1262 2.62619C35.5071 2.24526 36.0238 2.03125 36.5625 2.03125C40.0302 2.03125 43.464 2.71427 46.6677 4.04131C49.8715 5.36834 52.7825 7.31341 55.2345 9.76546C57.6866 12.2175 59.6317 15.1285 60.9587 18.3323C62.2857 21.536 62.9688 24.9698 62.9688 28.4375C62.9688 28.9762 62.7547 29.4929 62.3738 29.8738C61.9929 30.2547 61.4762 30.4688 60.9375 30.4688ZM38.5938 26.4062H58.9062C58.4486 21.1728 56.1616 16.2679 52.4468 12.5532C48.7321 8.83841 43.8272 6.55137 38.5938 6.09375V26.4062Z" />
    </svg>
  );
}

function Icon3() {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 65 65"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M32.5 62.9679C32.0697 62.9694 31.6501 62.8343 31.3016 62.5819L25.6953 58.5194C20.2588 54.5543 15.8343 49.3626 12.7813 43.3661C9.72834 37.3696 8.13304 30.7374 8.125 24.0085V12.1866C8.12518 11.7692 8.25397 11.3619 8.49385 11.0203C8.73373 10.6786 9.07304 10.4192 9.46562 10.2773L31.8094 2.15226C32.2557 1.99091 32.7443 1.99091 33.1906 2.15226L55.5344 10.2773C55.927 10.4192 56.2663 10.6786 56.5061 11.0203C56.746 11.3619 56.8748 11.7692 56.875 12.1866V24.0085C56.8702 30.7409 55.2764 37.377 52.2233 43.3773C49.1702 49.3776 44.744 54.5726 39.3047 58.5398L33.6984 62.6023C33.3475 62.8474 32.928 62.9753 32.5 62.9679ZM12.1875 13.6085V24.0085C12.2013 30.0907 13.6464 36.0842 16.4058 41.5044C19.1653 46.9245 23.1617 51.6191 28.0719 55.2085L32.5 58.4179L36.9281 55.2085C41.8383 51.6191 45.8347 46.9245 48.5942 41.5044C51.3536 36.0842 52.7987 30.0907 52.8125 24.0085V13.6085L32.5 6.21476L12.1875 13.6085Z" />
      <path d="M31.3016 52.5288L30.4688 51.9194C26.0667 48.7174 22.4839 44.52 20.0129 39.6697C17.5418 34.8194 16.2525 29.4536 16.25 24.0101V17.9163C16.2502 17.4989 16.379 17.0916 16.6188 16.75C16.8587 16.4083 17.198 16.1489 17.5906 16.0069L31.8094 10.8476C32.2557 10.6862 32.7443 10.6862 33.1906 10.8476L47.4094 16.0069C47.7955 16.1465 48.1303 16.3997 48.3696 16.7333C48.609 17.0669 48.7416 17.4652 48.75 17.8757V23.9694C48.7475 29.4129 47.4582 34.7787 44.9871 39.629C42.5161 44.4794 38.9333 48.6768 34.5312 51.8788L33.6984 52.4882C33.3549 52.7483 32.9375 52.8925 32.5066 52.8998C32.0758 52.9071 31.6537 52.7772 31.3016 52.5288ZM20.3125 19.2976V24.0101C20.3219 28.7385 21.4273 33.4004 23.542 37.6297C25.6566 41.859 28.7229 45.5405 32.5 48.3851C36.2771 45.5405 39.3434 41.859 41.458 37.6297C43.5727 33.4004 44.6781 28.7385 44.6875 24.0101V19.2976L32.5 14.8694L20.3125 19.2976Z" />
    </svg>
  );
}

function Icon4() {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 66 65"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M28.363 62.8054C27.9395 62.6241 27.5901 62.3042 27.3724 61.8982C27.1546 61.4922 27.0813 61.0242 27.1646 60.5711L30.7802 40.6242H12.9052C12.5322 40.625 12.1662 40.523 11.8473 40.3295C11.5284 40.1361 11.269 39.8585 11.0974 39.5273C10.9402 39.2062 10.8706 38.8492 10.8956 38.4926C10.9206 38.1359 11.0392 37.7922 11.2396 37.4961L35.6146 2.96481C35.8835 2.58598 36.2742 2.31079 36.7214 2.18511C37.1687 2.05943 37.6455 2.09087 38.0724 2.27418C38.4881 2.44537 38.8352 2.74962 39.0594 3.13925C39.2837 3.52889 39.3723 3.9819 39.3115 4.42731L35.6552 24.3742H53.5302C53.9032 24.3734 54.2692 24.4754 54.5881 24.6688C54.907 24.8623 55.1664 25.1399 55.338 25.4711C55.4952 25.7922 55.5648 26.1491 55.5398 26.5058C55.5149 26.8624 55.3962 27.2062 55.1958 27.5023L30.8208 62.0336C30.5636 62.4266 30.1781 62.7182 29.7299 62.859C29.2818 62.9997 28.7988 62.9808 28.363 62.8054ZM16.8255 36.5617H33.2177C33.5161 36.5612 33.811 36.6265 34.0813 36.7529C34.3516 36.8792 34.5908 37.0636 34.7818 37.2929C34.9703 37.5225 35.1063 37.7906 35.18 38.0784C35.2537 38.3662 35.2634 38.6666 35.2083 38.9586L32.7911 52.2429L49.6099 28.4367H33.2177C32.9193 28.4372 32.6244 28.3719 32.3541 28.2455C32.0838 28.1191 31.8446 27.9347 31.6536 27.7054C31.4651 27.4759 31.3291 27.2078 31.2554 26.92C31.1817 26.6322 31.1721 26.3317 31.2271 26.0398L33.6443 12.7554L16.8255 36.5617Z" />
    </svg>
  );
}

function Icon5() {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 66 65"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M61.6562 10.1562H4.78125C4.24253 10.1562 3.72587 10.3703 3.34494 10.7512C2.96401 11.1321 2.75 11.6488 2.75 12.1875V52.8125C2.75 53.3512 2.96401 53.8679 3.34494 54.2488C3.72587 54.6297 4.24253 54.8438 4.78125 54.8438H61.6562C62.195 54.8438 62.7116 54.6297 63.0926 54.2488C63.4735 53.8679 63.6875 53.3512 63.6875 52.8125V12.1875C63.6875 11.6488 63.4735 11.1321 63.0926 10.7512C62.7116 10.3703 62.195 10.1562 61.6562 10.1562ZM59.625 50.7812H6.8125V14.2188H59.625V50.7812Z" />
      <path d="M14.5507 45.8656L23.0617 33.9624L31.5726 45.8656C31.7161 46.1078 31.9084 46.3176 32.1373 46.4816C32.3661 46.6456 32.6266 46.7602 32.9022 46.8182C33.1777 46.8762 33.4623 46.8762 33.7379 46.8183C34.0134 46.7605 34.274 46.6459 34.5029 46.482C34.7318 46.3181 34.9242 46.1083 35.0677 45.8661C35.2113 45.6239 35.3029 45.3544 35.3368 45.0749C35.3707 44.7954 35.3461 44.5118 35.2646 44.2423C35.183 43.9728 35.0464 43.7232 34.8632 43.5093L31.857 39.2843L39.3117 29.7171L51.9257 45.9671C52.0813 46.1966 52.2823 46.3919 52.5162 46.5407C52.7502 46.6896 53.0121 46.7891 53.2859 46.8329C53.5597 46.8767 53.8396 46.864 54.1083 46.7956C54.377 46.7271 54.6289 46.6044 54.8484 46.4349C55.0678 46.2654 55.2503 46.0528 55.3845 45.8102C55.5187 45.5675 55.6018 45.2999 55.6286 45.024C55.6555 44.748 55.6256 44.4694 55.5407 44.2054C55.4558 43.9414 55.3178 43.6976 55.1351 43.489L40.9163 25.2078C40.7265 24.9631 40.4832 24.7651 40.205 24.6289C39.9269 24.4927 39.6213 24.4219 39.3117 24.4219C39.002 24.4219 38.6964 24.4927 38.4183 24.6289C38.1401 24.7651 37.8968 24.9631 37.707 25.2078L29.3992 35.8921L24.707 29.3312C24.5186 29.0711 24.2713 28.8593 23.9853 28.7133C23.6993 28.5673 23.3828 28.4911 23.0617 28.4911C22.7405 28.4911 22.424 28.5673 22.138 28.7133C21.852 28.8593 21.6047 29.0711 21.4163 29.3312L11.2601 43.5499C11.0011 43.9873 10.9154 44.5058 11.0201 45.0032C11.1247 45.5006 11.412 45.9406 11.8252 46.2366C12.2385 46.5325 12.7477 46.6627 13.2522 46.6015C13.7568 46.5404 14.2201 46.2923 14.5507 45.9062V45.8656Z" />
      <path d="M29.1562 24.375C30.2781 24.375 31.1875 23.4656 31.1875 22.3438C31.1875 21.2219 30.2781 20.3125 29.1562 20.3125C28.0344 20.3125 27.125 21.2219 27.125 22.3438C27.125 23.4656 28.0344 24.375 29.1562 24.375Z" />
    </svg>
  );
}

function Icon6() {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 65 65"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M39.3237 62.805L32.4987 59.9409L25.6737 62.805C25.2561 62.9821 24.7912 63.0144 24.3531 62.8966C23.9151 62.7788 23.529 62.5177 23.2566 62.155L18.7878 56.2644L11.4347 55.33C10.9853 55.2745 10.5673 55.0705 10.2471 54.7503C9.92697 54.4302 9.72295 54.0122 9.6675 53.5628L8.73312 46.2097L2.8425 41.7409C2.47979 41.4685 2.21874 41.0824 2.10093 40.6444C1.98313 40.2063 2.01537 39.7414 2.1925 39.3237L5.05656 32.4987L2.1925 25.6737C2.01537 25.2561 1.98313 24.7912 2.10093 24.3531C2.21874 23.9151 2.47979 23.529 2.8425 23.2566L8.73312 18.7878L9.6675 11.4347C9.72295 10.9853 9.92697 10.5673 10.2471 10.2471C10.5673 9.92697 10.9853 9.72295 11.4347 9.6675L18.7878 8.73312L23.2566 2.8425C23.529 2.47979 23.9151 2.21874 24.3531 2.10093C24.7912 1.98313 25.2561 2.01537 25.6737 2.1925L32.4987 5.05656L39.3237 2.1925C39.7414 2.01537 40.2063 1.98313 40.6444 2.10093C41.0824 2.21874 41.4685 2.47979 41.7409 2.8425L46.2097 8.73312L53.5628 9.6675C54.0122 9.72295 54.4302 9.92697 54.7503 10.2471C55.0705 10.5673 55.2745 10.9853 55.33 11.4347L56.2644 18.7878L62.155 23.2566C62.5177 23.529 62.7788 23.9151 62.8966 24.3531C63.0144 24.7912 62.9821 25.2561 62.805 25.6737L59.9409 32.4987L62.805 39.3237C62.9821 39.7414 63.0144 40.2063 62.8966 40.6444C62.7788 41.0824 62.5177 41.4685 62.155 41.7409L56.2644 46.2097L55.33 53.5628C55.2745 54.0122 55.0705 54.4302 54.7503 54.7503C54.4302 55.0705 54.0122 55.2745 53.5628 55.33L46.2097 56.2644L41.7409 62.155C41.4685 62.5177 41.0824 62.7788 40.6444 62.8966C40.2063 63.0144 39.7414 62.9821 39.3237 62.805ZM33.23 55.8581L39.3237 58.4581L43.3862 53.1159C43.5508 52.9037 43.7558 52.7262 43.9894 52.5937C44.223 52.4612 44.4806 52.3764 44.7472 52.3441L51.3691 51.4909L52.2222 44.8691C52.2545 44.6024 52.3393 44.3449 52.4718 44.1113C52.6043 43.8777 52.7818 43.6727 52.9941 43.5081L58.3362 39.4456L55.7362 33.3519C55.5328 32.8433 55.5328 32.276 55.7362 31.7675L58.3362 25.6737L52.9941 21.6112C52.7818 21.4467 52.6043 21.2417 52.4718 21.0081C52.3393 20.7745 52.2545 20.5169 52.2222 20.2503L51.3691 13.6284L44.7472 12.7753C44.4806 12.743 44.223 12.6582 43.9894 12.5257C43.7558 12.3932 43.5508 12.2157 43.3862 12.0034L39.3237 6.66125L33.23 9.26125C32.7215 9.46468 32.1542 9.46468 31.6456 9.26125L25.5519 6.66125L21.4894 12.0034C21.3248 12.2157 21.1198 12.3932 20.8862 12.5257C20.6526 12.6582 20.395 12.743 20.1284 12.7753L13.5066 13.6284L12.6534 20.1284C12.6211 20.395 12.5363 20.6526 12.4038 20.8862C12.2713 21.1198 12.0938 21.3248 11.8816 21.4894L6.53937 25.5519L9.13937 31.6456C9.34281 32.1542 9.34281 32.7215 9.13937 33.23L6.53937 39.3237L11.8816 43.3862C12.0938 43.5508 12.2713 43.7558 12.4038 43.9894C12.5363 44.223 12.6211 44.4806 12.6534 44.7472L13.5066 51.3691L20.1284 52.2222C20.395 52.2545 20.6526 52.3393 20.8862 52.4718C21.1198 52.6043 21.3248 52.7818 21.4894 52.9941L25.5519 58.3362L31.6456 55.7362C31.9142 55.6463 32.198 55.6107 32.4805 55.6317C32.763 55.6526 33.0385 55.7296 33.2909 55.8581H33.23Z" />
      <path d="M28.6816 46.7192C28.3436 46.7208 28.0106 46.638 27.7126 46.4784C27.4147 46.3188 27.1613 46.0874 26.9754 45.8051L18.6066 33.0692C18.4599 32.8451 18.3588 32.5943 18.309 32.3312C18.2592 32.068 18.2617 31.7976 18.3164 31.5354C18.3711 31.2733 18.4769 31.0244 18.6278 30.8031C18.7787 30.5818 18.9716 30.3924 19.1957 30.2457C19.6482 29.9494 20.1999 29.845 20.7294 29.9555C20.9916 30.0102 21.2404 30.116 21.4617 30.2669C21.683 30.4177 21.8724 30.6107 22.0191 30.8348L28.6816 40.991L42.9004 19.1957C43.1967 18.7432 43.6606 18.4269 44.1901 18.3164C44.7196 18.206 45.2713 18.3104 45.7238 18.6067C46.1764 18.903 46.4927 19.3669 46.6031 19.8964C46.7136 20.4259 46.6092 20.9776 46.3129 21.4301L30.4691 45.8051C30.2759 46.0993 30.0096 46.3382 29.6962 46.4984C29.3829 46.6586 29.0333 46.7347 28.6816 46.7192Z" />
    </svg>
  );
}

export const serviceSectionData: ServiceSectionProps = {
  services: [
    {
      icon: <FaMobileAlt />,
      //  <Icon1 />,
      title: 'Application Development',
      description:
        "Emphasize the company's ability to deliver solutions that drive business results and improve operations. From concept to deployment - seamless application development services.",
      slug: '/services/app-development',
    },
    {
      icon: <FaLaptop />,
      title: 'Website Development',
      description:
        "Transforming online presence into powerful sales engines. Focus on the company's ability to deliver solutions that drive business results, such as increased conversions or sales.",
      slug: '/services/web-development',
    },
    {
      icon: <SiMaterialdesign />,
      title: 'UI / UX Design',
      description:
        'Crafting visually appealing and functional interfaces that captivate your audience and achieve your business goals. Elevate your brand with designs that are not only beautiful but functional',
      slug: '/services/ux',
    },
    {
      icon: <MdCloudCircle />,
      title: 'Cloud Solutions',
      description:
        'Our team of experts has extensive experience in designing and implementing scalable, secure, and efficient cloud solutions.',
      slug: '/services/cloud-solutions',
    },
    {
      icon: <WiStars />,
      title: 'Custom AI Solution',
      description:
        'Harness the power of AI to solve complex challenges. From machine learning to deep learning, we deliver cutting-edge AI solutions.',
      slug: '/services/custom-ai',
    },
    // {
    //   icon: <TiSocialInstagram />,
    //   title: 'Social Media Marketing',
    //   description:
    //     'Engage your audience and drive results with our social media marketing. Tailored social media strategies to reach your target audience.',
    //   slug: '/services/social-marketing',
    // },
    {
      icon: <SiCrowdsource />,
      title: 'Software Outsourcing',
      description:
        'Your vision, our expertise. Seamless software outsourcing. Reduce costs, accelerate development, and ensure quality with our outsourcing solutions.',
      slug: '/services/software-outsourcing',
    },
  ],
};
