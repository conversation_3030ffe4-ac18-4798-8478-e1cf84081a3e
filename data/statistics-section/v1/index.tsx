import { StatisticsSectionProps } from '@/src/sections/statistics/v1';

function Icon1() {
  return (
    <svg
      width={40}
      height={40}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3114_85)">
        <path
          d="M8.47057 25.272L8.30386 24.5886L7.60437 24.5145L3.44531 24.0741V23.4492H13.1554C13.6399 26.7944 16.5191 29.3642 19.9986 29.3642C23.4781 29.3642 26.3573 26.7944 26.8418 23.4492H36.5519V24.0741L32.3928 24.5145L31.6933 24.5886L31.5266 25.272C31.2361 26.4628 30.7657 27.5835 30.1455 28.6037L29.78 29.2048L30.2223 29.7519L32.8526 33.0052L30.5546 35.3032L27.3013 32.6729L26.7542 32.2306L26.1531 32.5961C25.1329 33.2163 24.0122 33.6867 22.8214 33.9772L22.138 34.144L22.0639 34.8434L21.6235 39.0025H18.3737L17.9333 34.8434L17.8592 34.144L17.1758 33.9772C15.985 33.6867 14.8643 33.2163 13.8441 32.5961L13.243 32.2306L12.6959 32.6729L9.44264 35.3032L7.14462 33.0052L9.7749 29.7519L10.2172 29.2048L9.85174 28.6037C9.23149 27.5835 8.76108 26.4628 8.47057 25.272Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M16.4688 4.53383C16.4688 2.58619 18.0549 1 20.0026 1C21.9502 1 23.5364 2.58619 23.5364 4.53383C23.5364 6.48144 21.9502 8.06758 20.0026 8.06758C18.0549 8.06758 16.4688 6.48144 16.4688 4.53383Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M15.1714 11.3203C15.1714 11.3203 15.1714 11.3203 15.1714 11.3203H24.8256C26.4959 11.3203 27.8564 12.6808 27.8564 14.3511V19.1022H12.1406V14.3511C12.1406 12.6808 13.5011 11.3204 15.1714 11.3203Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M7.32031 6.15523C7.32031 4.46805 8.69456 3.09375 10.3818 3.09375C12.069 3.09375 13.4433 4.46799 13.4433 6.15523C13.4433 6.53521 13.3739 6.89812 13.2473 7.23309C11.9658 7.58005 10.8238 8.26377 9.92085 9.181C8.45111 8.95792 7.32031 7.68532 7.32031 6.15523Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M3.44531 14.9507C3.44531 13.5119 4.61743 12.3398 6.05617 12.3398H8.07626C7.89441 12.9798 7.79695 13.6548 7.79695 14.3515V19.1027H3.44531V14.9507Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M26.5547 6.15523C26.5547 4.46805 27.9289 3.09375 29.6162 3.09375C31.3034 3.09375 32.6777 4.46799 32.6777 6.15523C32.6777 7.68535 31.5468 8.95797 30.0771 9.18101C29.1742 8.26377 28.0322 7.58005 26.7506 7.23309C26.624 6.89813 26.5547 6.5352 26.5547 6.15523Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M32.1955 14.3515C32.1955 13.6548 32.0981 12.9798 31.9162 12.3398H33.9363C35.3751 12.3398 36.5472 13.5119 36.5472 14.9506V19.1026H32.1955V14.3515Z"
          stroke="white"
          strokeWidth={2}
        />
      </g>
      <defs>
        <clipPath id="clip0_3114_85">
          <rect width={40} height={40} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function Icon2() {
  return (
    <svg
      width={40}
      height={40}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M30.201 24.3999L31.4084 24.0315C31.4845 24.0083 31.558 23.9861 31.6291 23.9647C32.0188 23.8471 32.336 23.7514 32.6153 23.6256C32.9374 23.4807 33.0585 23.3578 33.1071 23.2739L33.1072 23.2738C33.1468 23.2054 33.1873 23.0789 33.1785 22.8282C33.1694 22.5711 33.1127 22.2675 33.0262 21.8854C33.0098 21.8128 32.9919 21.7359 32.9733 21.656C32.9023 21.3513 32.821 21.002 32.7701 20.6734C32.7054 20.2548 32.6673 19.7484 32.794 19.2468L32.794 19.2466C32.9208 18.7451 33.1869 18.315 33.442 17.9695C33.6516 17.6856 33.8976 17.4033 34.1109 17.1584C34.1549 17.1078 34.1976 17.0588 34.2383 17.0118C34.4957 16.7139 34.6915 16.4726 34.8258 16.249C34.9577 16.0292 34.985 15.8985 34.985 15.8237C34.985 15.7584 34.9555 15.6081 34.8023 15.3367C34.6529 15.072 34.4385 14.7815 34.173 14.4397C34.1266 14.38 34.0783 14.3182 34.0286 14.2548C33.812 13.9781 33.5714 13.6708 33.3664 13.3708C33.1165 13.005 32.8541 12.5598 32.7299 12.0718L32.7299 12.0717C32.6023 11.5704 32.6395 11.0639 32.7035 10.6452C32.7537 10.3164 32.8344 9.96702 32.9048 9.66213C32.9233 9.58222 32.941 9.50536 32.9573 9.43273C33.0431 9.05044 33.0993 8.74677 33.1078 8.48969C33.1162 8.239 33.0755 8.11266 33.0357 8.04436L33.0357 8.04433C32.9959 7.97598 32.9062 7.87809 32.6841 7.76147C32.4564 7.64187 32.1647 7.54057 31.7899 7.42624C31.7188 7.40452 31.6432 7.38197 31.5646 7.35851C31.2648 7.26901 30.9211 7.16644 30.6105 7.04764C30.2149 6.89631 29.7562 6.67825 29.3834 6.31954L29.3834 6.31948C29.0079 5.9581 28.7671 5.50858 28.5955 5.11375C28.4563 4.79366 28.3357 4.44209 28.2311 4.13734C28.2078 4.06937 28.1853 4.00373 28.1636 3.94129C28.0338 3.56872 27.9227 3.28133 27.7967 3.05891C27.6733 2.84108 27.5779 2.76315 27.5213 2.73289C27.4627 2.70162 27.3425 2.66535 27.0908 2.68328C26.8338 2.7016 26.5312 2.7683 26.1481 2.86623C26.0851 2.88233 26.0191 2.89951 25.9509 2.91728C25.6375 2.99888 25.2759 3.09304 24.9306 3.15371C24.5068 3.22815 23.9979 3.2749 23.4888 3.15757C22.9847 3.04139 22.5515 2.78039 22.2055 2.53087C21.9205 2.3254 21.6359 2.08265 21.3889 1.8719C21.338 1.82847 21.2887 1.78639 21.2412 1.74628C20.9406 1.49191 20.6952 1.29674 20.4668 1.16246C20.2421 1.03042 20.1036 1 20.019 1C19.9254 1 19.7145 1.04743 19.3715 1.2249C19.0484 1.39212 18.6968 1.62751 18.3461 1.88717C18.062 2.09752 17.7999 2.30744 17.563 2.49722C17.5075 2.5416 17.4535 2.58488 17.4009 2.6268C17.2685 2.73228 17.1348 2.83742 17.0188 2.92037C16.961 2.96175 16.8928 3.00836 16.8213 3.0502C16.7696 3.08048 16.6343 3.15843 16.4599 3.19898L30.201 24.3999ZM30.201 24.3999L30.8359 25.491M30.201 24.3999L30.8359 25.491M30.8359 25.491L33.7525 30.5035L35.3657 33.2761L35.8795 34.1592L35.9005 34.1952L32.7085 34.3343C32.1437 34.3589 31.58 34.5671 31.1174 34.8363C30.6547 35.1055 30.1954 35.4926 29.8949 35.9709L29.8947 35.9712L28.1961 38.6774L28.1411 38.5827L27.5442 37.5567L25.7329 34.4431L23.9212 31.329L23.324 30.3024L23.1566 30.0146L23.1125 29.9387L23.1095 29.9336C23.035 29.7938 22.9147 29.627 22.7215 29.4969C22.4913 29.3418 22.2326 29.2897 21.9967 29.3119C21.6243 29.347 21.3764 29.562 21.3323 29.6001C21.3307 29.6016 21.3293 29.6028 21.3282 29.6037L21.3278 29.604C21.0315 29.8568 20.7866 30.0495 20.5606 30.1805C20.3387 30.3091 20.197 30.3408 20.1054 30.3408C19.9943 30.3408 19.7894 30.2936 19.5013 30.1453C19.2265 30.0037 18.9443 29.8054 18.7006 29.5978L18.6996 29.597C18.4977 29.4256 18.2029 29.2999 17.8614 29.3273C17.4994 29.3563 17.2168 29.545 17.0294 29.764L17.0293 29.764L17.0207 29.7743C16.9762 29.8319 16.9331 29.8973 16.9302 29.9021L16.9269 29.9077L16.9245 29.9118L16.9208 29.9183L16.9089 29.9387L16.8644 30.0154L16.6966 30.305L16.0984 31.3374L14.2846 34.4686L12.4711 37.5993L11.8735 38.631L11.8177 38.7274M30.8359 25.491L11.8177 38.7274M11.8177 38.7274L10.1143 36.0244L10.1142 36.0242C9.81275 35.5462 9.35263 35.1599 8.8895 34.8916C8.42635 34.6232 7.8624 34.4163 7.29785 34.3925L7.29747 34.3925L4.10525 34.2594L4.13129 34.2144L4.65498 33.3106L6.28254 30.5017L9.04258 25.7381C9.15311 25.5625 9.23997 25.3312 9.23062 25.0595C9.22051 24.7653 9.10146 24.5338 8.97728 24.3776C8.76016 24.1046 8.47587 23.9866 8.32114 23.9403C7.89448 23.8126 7.55984 23.7077 7.28942 23.5757C7.02501 23.4466 6.92195 23.3377 6.87831 23.2628C6.83848 23.1943 6.79773 23.0678 6.8061 22.8171C6.81469 22.56 6.87081 22.2563 6.95663 21.874C6.97293 21.8014 6.99068 21.7245 7.00913 21.6446C7.07951 21.3397 7.16018 20.9903 7.21042 20.6616C7.27442 20.2428 7.31153 19.7363 7.18395 19.235C7.05628 18.7333 6.78913 18.3035 6.53324 17.9585C6.32305 17.6752 6.07657 17.3935 5.86282 17.1492C5.81846 17.0985 5.77551 17.0494 5.73458 17.0023C5.47649 16.7049 5.28026 16.4641 5.14585 16.241C5.01374 16.0217 4.98664 15.8917 4.98664 15.8175C4.98664 15.6897 5.03055 15.513 5.1719 15.2597C5.31499 15.0033 5.52187 14.7307 5.78272 14.4118C5.83965 14.3423 5.90062 14.2688 5.96385 14.1926C6.16776 13.9469 6.39522 13.6728 6.58683 13.4087C6.83937 13.0606 7.11006 12.626 7.23523 12.1302L7.23524 12.1301C7.36184 11.6285 7.32376 11.1221 7.25897 10.7034C7.2081 10.3747 7.12677 10.0254 7.0558 9.72067C7.0372 9.64081 7.01932 9.56401 7.00288 9.49144C6.91633 9.10932 6.85965 8.80579 6.85058 8.54877C6.84173 8.29813 6.88225 8.17176 6.92185 8.10341L6.92186 8.1034C6.96152 8.03494 7.05108 7.93687 7.27295 7.81983C7.50046 7.69981 7.792 7.59797 8.16653 7.48295C8.2377 7.46109 8.31327 7.43839 8.39184 7.41478C8.69148 7.32474 9.03488 7.22155 9.34525 7.1022C9.74062 6.95017 10.1989 6.73129 10.571 6.37195L10.5711 6.37189C10.946 6.00984 11.1859 5.55986 11.3568 5.16474C11.4954 4.8444 11.6153 4.4926 11.7193 4.18765C11.7425 4.11964 11.7649 4.05395 11.7865 3.99147C11.9156 3.61865 12.0262 3.33104 12.1518 3.10838C12.2748 2.89031 12.3701 2.81223 12.4266 2.78187L12.4268 2.78175C12.4852 2.75037 12.6052 2.71378 12.8571 2.73124C13.1141 2.74905 13.4167 2.81519 13.8 2.91243C13.8631 2.92843 13.9291 2.94551 13.9975 2.96318C14.3109 3.0442 14.6726 3.1377 15.018 3.19774C15.4419 3.27142 15.9509 3.31728 16.4598 3.19898L11.8177 38.7274ZM13.1421 26.4073L13.1421 26.4073C7.44646 22.759 5.57247 15.2148 8.99664 9.30476M13.1421 26.4073L26.1015 4.5036M13.1421 26.4073L13.148 26.411C17.0308 28.8601 22.1187 29.1272 26.3604 26.6595C32.4302 23.1283 34.4946 15.3193 30.9634 9.2495L30.9634 9.24947C29.8085 7.2644 28.1942 5.70637 26.333 4.63328L26.3286 4.63081C26.2854 4.60617 26.2414 4.5814 26.1968 4.5567M13.1421 26.4073L26.1968 4.5567M8.99664 9.30476L9.8619 9.80608L8.99664 9.30476ZM8.99664 9.30476C12.4597 3.32683 20.0698 1.21854 26.1015 4.5036M26.1015 4.5036C26.1293 4.51871 26.1599 4.53594 26.1818 4.54826C26.1875 4.55144 26.1925 4.55429 26.1968 4.5567M26.1015 4.5036L26.1968 4.5567M36.093 34.4774C36.093 34.4773 36.0931 34.4773 36.0931 34.4773C36.115 34.4646 36.1595 34.4387 36.2498 34.3861L36.093 34.4774ZM28.3462 38.9843L28.1892 39.0757C28.2795 39.0231 28.3241 38.9971 28.3461 38.9843C28.3461 38.9843 28.3461 38.9843 28.3462 38.9843ZM3.91305 34.5417L3.75628 34.4508C3.84647 34.5031 3.89103 34.5289 3.91303 34.5416C3.91303 34.5416 3.91305 34.5417 3.91305 34.5417Z"
        stroke="white"
        strokeWidth={2}
      />
      <path
        d="M24.4629 8.00983L24.463 8.00991C28.6958 10.4621 30.1447 15.9024 27.6926 20.1353C25.2404 24.3679 19.8 25.8171 15.5672 23.3649L15.5671 23.3648C15.4661 23.3063 15.3644 23.2438 15.2589 23.177L15.259 23.177L15.2526 23.173C14.0664 22.4342 13.0385 21.4052 12.2874 20.1142L12.2874 20.1141C9.82754 15.886 11.2669 10.4427 15.4952 7.98273L15.4952 7.98272C18.3426 6.32614 21.7428 6.43638 24.4145 7.98268L24.4181 7.9847C24.4337 7.99366 24.4489 8.00204 24.4549 8.00536C24.4601 8.00828 24.4623 8.00949 24.4629 8.00983ZM20.7404 21.3954L22.7792 22.4672L22.7792 22.4672C23.2338 22.7062 23.9556 22.8946 24.5787 22.4418C25.2019 21.9891 25.2457 21.2444 25.1589 20.7382C25.1589 20.7382 25.1589 20.7382 25.1589 20.7382L24.7695 18.4681L24.7695 18.4679C24.7437 18.3177 24.7595 18.0646 24.8528 17.7772C24.9462 17.4897 25.0822 17.2757 25.1913 17.1694L25.1913 17.1694L26.8407 15.5617C26.8407 15.5617 26.8408 15.5616 26.8408 15.5616C27.2087 15.2031 27.6107 14.5749 27.3728 13.8424C27.1349 13.11 26.4404 12.8379 25.9321 12.764L25.9319 12.7639L23.6526 12.4327L23.6526 12.4327C23.5017 12.4108 23.2658 12.3175 23.0213 12.1399C22.7768 11.9623 22.6152 11.7667 22.5478 11.63L22.5478 11.63L21.5284 9.56476C21.5283 9.56472 21.5283 9.56467 21.5283 9.56463C21.301 9.10399 20.8277 8.52745 20.0576 8.52745C19.2874 8.52745 18.8141 9.10402 18.5868 9.56464L18.5868 9.56467L17.5674 11.63C17.5 11.7667 17.3384 11.9622 17.0938 12.1399C16.8493 12.3175 16.6134 12.4108 16.4626 12.4327L16.6064 13.4223L16.4626 12.4327L14.1833 12.7639L14.1831 12.764C13.6749 12.8379 12.9803 13.1099 12.7423 13.8423C12.5043 14.5748 12.9064 15.203 13.2743 15.5616C13.2743 15.5617 13.2744 15.5617 13.2744 15.5617L14.9236 17.1693L14.9237 17.1694C15.0328 17.2757 15.1688 17.4897 15.2622 17.7772C15.3556 18.0647 15.3714 18.3178 15.3456 18.468L15.3456 18.4681L14.9562 20.7382C14.9562 20.7382 14.9562 20.7382 14.9562 20.7382C14.8694 21.2445 14.9133 21.9892 15.5365 22.4419C16.1596 22.8946 16.8814 22.7062 17.336 22.4672L17.3361 22.4672L19.3747 21.3954C19.3747 21.3954 19.3747 21.3953 19.3748 21.3953C19.5097 21.3244 19.7554 21.2612 20.0575 21.2612C20.3598 21.2612 20.6054 21.3244 20.7403 21.3953C20.7404 21.3953 20.7404 21.3954 20.7404 21.3954Z"
        stroke="white"
        strokeWidth={2}
      />
    </svg>
  );
}

function Icon3() {
  return (
    <svg
      width={40}
      height={40}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1_3880)">
        <path
          d="M15.2344 19.8281C15.3282 19.8281 15.4062 19.9062 15.4062 20C15.4062 20.0938 15.3282 20.1719 15.2344 20.1719C15.1406 20.1719 15.0625 20.0938 15.0625 20C15.0625 19.9062 15.1406 19.8281 15.2344 19.8281Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M15.2344 15.1406C15.2918 15.1406 15.3491 15.1416 15.4062 15.1436V15.4876C15.3492 15.4855 15.2919 15.4844 15.2344 15.4844C12.7438 15.4844 10.7188 17.5094 10.7188 20C10.7188 22.4906 12.7438 24.5156 15.2344 24.5156C15.2919 24.5156 15.3492 24.5145 15.4062 24.5124V24.8564C15.3491 24.8584 15.2918 24.8594 15.2344 24.8594C12.5562 24.8594 10.375 22.6782 10.375 20C10.375 17.3218 12.5562 15.1406 15.2344 15.1406Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M15.4062 5.76562H12.8906C12.796 5.76562 12.7188 5.84291 12.7188 5.9375V7.59687V8.35211L11.9923 8.55872C11.1098 8.80974 10.2584 9.16443 9.44688 9.62137L8.78569 9.99365L8.24914 9.45711L7.06946 8.27742C7.00217 8.21013 6.89393 8.21013 6.82664 8.27742L3.51179 11.5923C3.44538 11.6587 3.44515 11.7683 3.51169 11.835C3.51173 11.835 3.51176 11.835 3.51179 11.8351L4.69125 13.0138L5.22795 13.5501L4.85593 14.2114C4.39883 15.0239 4.04411 15.8755 3.7931 16.758L3.58648 17.4844H2.83125H1.17188C1.07728 17.4844 1 17.5617 1 17.6562V22.3438C1 22.4383 1.07728 22.5156 1.17188 22.5156H2.83125H3.58648L3.7931 23.242C4.04412 24.1246 4.39881 24.976 4.85574 25.7875L5.22803 26.4487L4.69148 26.9852L3.5118 28.1649C3.44515 28.2316 3.44535 28.3413 3.51179 28.4077L6.82664 31.7226L6.11953 32.4297L6.82664 31.7226C6.89385 31.7898 7.00192 31.7899 7.06922 31.7228L15.4062 5.76562ZM15.4062 5.76562V10.7984C15.349 10.7974 15.2917 10.7969 15.2344 10.7969C10.1587 10.7969 6.03125 14.9243 6.03125 20C6.03125 25.0757 10.1587 29.2031 15.2344 29.2031C15.2917 29.2031 15.349 29.2026 15.4062 29.2016V34.2344H12.8906C12.796 34.2344 12.7188 34.1571 12.7188 34.0625V32.4031V31.6479L11.9923 31.4413C11.1103 31.1904 10.2585 30.8349 9.44518 30.3781L8.78409 30.0068L8.24813 30.5431L7.06946 31.7226L15.4062 5.76562Z"
          stroke="white"
          strokeWidth={2}
        />
        <path
          d="M36.8998 10.7969H33.9688V7.86578L36.8998 10.7969ZM27.1094 10.7969H22.2656C21.0665 10.7969 20.0938 11.7696 20.0938 12.9688C20.0938 14.1679 21.0665 15.1406 22.2656 15.1406H27.1094C28.3085 15.1406 29.2812 14.1679 29.2812 12.9688C29.2812 11.7696 28.3085 10.7969 27.1094 10.7969ZM22.2656 29.2031H34.1406C35.3398 29.2031 36.3125 28.2304 36.3125 27.0312C36.3125 25.8321 35.3398 24.8594 34.1406 24.8594H22.2656C21.0665 24.8594 20.0938 25.8321 20.0938 27.0312C20.0938 28.2304 21.0665 29.2031 22.2656 29.2031ZM22.2656 24.5156H34.1406C35.3398 24.5156 36.3125 23.5429 36.3125 22.3438C36.3125 21.1446 35.3398 20.1719 34.1406 20.1719H22.2656C21.0665 20.1719 20.0938 21.1446 20.0938 22.3438C20.0938 23.5429 21.0665 24.5156 22.2656 24.5156ZM22.2656 19.8281H34.1406C35.3398 19.8281 36.3125 18.8554 36.3125 17.6562C36.3125 16.4571 35.3398 15.4844 34.1406 15.4844H22.2656C21.0665 15.4844 20.0938 16.4571 20.0938 17.6562C20.0938 18.8554 21.0665 19.8281 22.2656 19.8281ZM29.625 12.9688C29.625 14.1679 30.5977 15.1406 31.7969 15.1406H39V34.0625C39 34.1571 38.9227 34.2344 38.8281 34.2344H19.75V5.76562H29.625V12.9688Z"
          stroke="white"
          strokeWidth={2}
        />
      </g>
      <defs>
        <clipPath id="clip0_1_3880">
          <rect width={40} height={40} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function Icon4() {
  return (
    <svg
      width={40}
      height={40}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.2083 8.16388L17.9596 7.24922L17.9595 7.24872C17.8319 6.77864 17.9928 6.28194 18.3735 5.97623L19.1121 5.38259L18.1653 5.33645C18.1653 5.33645 18.1653 5.33644 18.1652 5.33644C17.6783 5.31272 17.2557 5.00597 17.0825 4.54985L18.2083 8.16388ZM18.2083 8.16388L17.4154 7.64482C17.4153 7.64478 17.4152 7.64474 17.4152 7.64469C17.0071 7.37756 16.485 7.37756 16.077 7.6447C16.0769 7.64474 16.0769 7.64478 16.0768 7.64482L15.2839 8.16389L15.5325 7.24911L15.5327 7.24864C15.66 6.77937 15.5 6.28257 15.119 5.97639C15.1189 5.97631 15.1188 5.97623 15.1187 5.97615L14.3802 5.38251L15.3269 5.33637C15.3269 5.33637 15.327 5.33637 15.327 5.33636C15.8146 5.31261 16.2365 5.00534 16.4095 4.5503L16.4097 4.54977L16.7461 3.66378L17.0824 4.54961L18.2083 8.16388ZM8.79717 9.34986L7.85042 9.30371C7.85038 9.30371 7.85034 9.30371 7.8503 9.30371C7.3627 9.27996 6.94088 8.97268 6.76781 8.51765L6.76759 8.51706L6.43122 7.63097L6.09499 8.51653C6.09496 8.51661 6.09493 8.51669 6.0949 8.51677C6.09486 8.51686 6.09483 8.51695 6.0948 8.51704C5.92146 8.97372 5.49851 9.27994 5.01211 9.30363C5.01207 9.30363 5.01202 9.30363 5.01198 9.30364L4.06523 9.34978L4.80412 9.94365L4.80435 9.94384C5.18406 10.2492 5.34567 10.7455 5.21762 11.2164L8.79717 9.34986ZM8.79717 9.34986L8.05828 9.94373L8.05806 9.94392C7.67835 10.2493 7.51674 10.7456 7.64478 11.2165C7.64481 11.2166 7.64484 11.2167 7.64487 11.2168L7.89347 12.1312L7.10045 11.6121C7.10039 11.612 7.10032 11.612 7.10026 11.612C6.69221 11.3448 6.17011 11.3448 5.76206 11.612C5.762 11.612 5.76194 11.612 5.76187 11.6121L4.96893 12.1312L5.21753 11.2167L8.79717 9.34986ZM29.4265 9.34986L28.4798 9.30371C28.4798 9.30371 28.4797 9.30371 28.4797 9.30371C27.9921 9.27996 27.5702 8.97269 27.3972 8.51764L27.3969 8.51702L27.0607 7.63101L26.7244 8.51651L26.7243 8.51704L25.7894 8.16208C25.7574 8.24638 25.6829 8.30044 25.5929 8.30482L29.4265 9.34986ZM29.4265 9.34986L28.6877 9.94373L28.6874 9.94392C28.3077 10.2493 28.1461 10.7456 28.2742 11.2165C28.2742 11.2166 28.2742 11.2167 28.2742 11.2168L28.5229 12.1311L27.7299 11.6121C27.7298 11.612 27.7298 11.612 27.7297 11.612C27.3217 11.3448 26.7996 11.3448 26.3915 11.612C26.3914 11.612 26.3914 11.6121 26.3913 11.6121L25.5984 12.1312L25.847 11.2167C25.9752 10.7458 25.8136 10.2493 25.4338 9.94384L25.4336 9.94365L24.6947 9.34978L25.6414 9.30364C25.6415 9.30363 25.6415 9.30363 25.6416 9.30363L29.4265 9.34986ZM22.6289 34.0944L5.87655 34.0945C6.93653 29.0644 11.4013 25.2884 16.7462 25.2884C19.2843 25.2884 21.6217 26.1389 23.4922 27.5713C22.7777 28.8671 22.3707 30.3567 22.3707 31.9396C22.3707 32.6814 22.4601 33.4032 22.6289 34.0944ZM5.81253 34.4229L5.81257 34.4226L5.81253 34.4229ZM30.533 31.1225L29.7219 30.3115C28.9369 29.5265 27.6641 29.5266 26.8791 30.3114L26.8791 30.3115C26.0942 31.0964 26.0942 32.3693 26.8791 33.1542L29.1173 35.3924C29.9022 36.1773 31.1751 36.1773 31.96 35.3924C32.8105 34.5419 33.6582 33.6821 34.5029 32.8252C34.9993 32.3218 35.4946 31.8194 35.989 31.3204C36.7675 30.5347 36.7664 29.2648 35.9794 28.4822C35.194 27.7011 33.9201 27.7015 33.1374 28.4921L33.1373 28.4921L30.533 31.1225ZM31.431 26.1292C34.64 26.1292 37.2414 28.7306 37.2414 31.9396C37.2414 35.1485 34.64 37.75 31.431 37.75C28.222 37.75 25.6206 35.1485 25.6206 31.9396C25.6206 28.7306 28.222 26.1292 31.431 26.1292ZM16.7462 12.2624C19.5752 12.2624 21.8686 14.5558 21.8686 17.3847C21.8686 20.2137 19.5752 22.5071 16.7462 22.5071C13.9173 22.5071 11.6239 20.2137 11.6239 17.3847C11.6238 14.5558 13.9172 12.2624 16.7462 12.2624Z"
        stroke="white"
        strokeWidth={2}
      />
    </svg>
  );
}

export const statisticsSectionData: StatisticsSectionProps = {
  statistics: [
    {
      icon: <Icon1 />,
      value: 8,
      counterSuffix: '+',
      title: 'Team Member',
    },
    {
      icon: <Icon2 />,
      value: 30,
      counterSuffix: '+',
      title: 'Project Done',
    },
    {
      icon: <Icon3 />,
      value: 20,
      counterSuffix: '+',
      title: 'Happy Clients',
    },
    // {
    //   icon: <Icon4 />,
    //   value: 160,
    //   counterSuffix: '+',
    //   title: 'Winning Award',
    // },
  ],
};
