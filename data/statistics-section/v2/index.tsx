import { StatisticsSectionProps } from '@/src/sections/statistics/v2';

function Icon1() {
  return (
    <svg
      width={31}
      height={31}
      viewBox="0 0 31 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3115_1299)">
        <path
          d="M29.0683 19.2494V17.3555H20.1311C20.1311 19.8061 18.0975 21.7927 15.5889 21.7927C13.0802 21.7927 11.0466 19.8061 11.0466 17.3555H2.10938V19.2494L5.99024 19.6508C6.23232 20.6202 6.62425 21.5322 7.14074 22.3621L4.68652 25.3274L7.42829 28.0058L10.4638 25.6083C11.3133 26.1129 12.2469 26.4957 13.2392 26.7322L13.6502 30.5234H17.5276L17.9385 26.7322C18.9308 26.4957 19.8644 26.1129 20.7139 25.6083L23.7494 28.0058L26.4912 25.3274L24.037 22.3621C24.5535 21.5322 24.9454 20.6202 25.1875 19.6508L29.0683 19.2494Z"
          fill="white"
        />
        <path
          d="M15.591 0.515625C13.6712 0.515625 12.1094 2.0414 12.1094 3.91676C12.1094 5.79212 13.6712 7.31783 15.591 7.31783C17.5107 7.31783 19.0726 5.79212 19.0726 3.91676C19.0726 2.0414 17.5107 0.515625 15.591 0.515625Z"
          fill="white"
        />
        <path
          d="M19.298 8.25781H11.8844C10.1776 8.25787 8.78906 9.61427 8.78906 11.2816V15.5959H22.3933V11.2816C22.3933 9.61427 21.0048 8.25781 19.298 8.25781Z"
          fill="white"
        />
        <path
          d="M8.20482 2.08594C6.48505 2.08594 5.08594 3.45277 5.08594 5.13273C5.08594 6.79922 6.46285 8.15673 8.16361 8.17848C8.86793 7.37369 9.83977 6.79928 10.9439 6.58859C11.1859 6.15578 11.3237 5.65973 11.3237 5.13273C11.3237 3.45271 9.9246 2.08594 8.20482 2.08594Z"
          fill="white"
        />
        <path
          d="M4.88222 9.02344C3.35328 9.02344 2.10938 10.2385 2.10938 11.7322V15.597H6.98692V11.2827C6.98692 10.466 7.19797 9.69665 7.56903 9.02344H4.88222Z"
          fill="white"
        />
        <path
          d="M22.9783 2.08594C21.2585 2.08594 19.8594 3.45277 19.8594 5.13273C19.8594 5.65967 19.9971 6.15578 20.2391 6.58859C21.3433 6.79928 22.3152 7.37369 23.0195 8.17848C24.7202 8.15679 26.0971 6.79922 26.0971 5.13273C26.0971 3.45271 24.698 2.08594 22.9783 2.08594Z"
          fill="white"
        />
        <path
          d="M26.2962 9.02344H23.6094C23.9804 9.69665 24.1915 10.466 24.1915 11.2827V15.597H29.069V11.7321C29.069 10.2385 27.8251 9.02344 26.2962 9.02344Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3115_1299">
          <rect
            width="30.7167"
            height="30.0067"
            fill="white"
            transform="translate(0.234375 0.515625)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

function Icon2() {
  return (
    <svg
      width={30}
      height={31}
      viewBox="0 0 30 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3115_1330)">
        <path
          d="M27.7041 26.0114C27.7041 26.0114 24.3744 20.2874 23.7771 19.2608C24.4566 19.0535 25.1805 18.8704 25.4813 18.3509C25.9826 17.4854 25.0903 16.0655 25.3246 15.1377C25.566 14.1829 26.9907 13.3566 26.9907 12.3861C26.9907 11.4405 25.5177 10.3358 25.2762 9.38652C25.0403 8.45893 25.9299 7.03749 25.427 6.17298C24.9241 5.3084 23.2491 5.37943 22.5596 4.71579C21.8501 4.03284 21.8523 2.36189 20.9966 1.90422C20.1374 1.44484 18.741 2.37325 17.787 2.15333C16.8439 1.93593 16.0013 0.515625 15.0162 0.515625C14.0165 0.515625 12.4264 2.12673 12.177 2.18471C11.2235 2.40641 9.82536 1.48028 8.96705 1.9416C8.11214 2.40082 8.11741 4.07193 7.40924 4.75601C6.72086 5.42087 5.04574 5.35292 4.54439 6.21848C4.04312 7.08387 4.9354 8.50344 4.70118 9.43169C4.46632 10.3623 2.99194 11.3052 2.99194 12.3814C2.99194 13.3521 4.42027 14.1757 4.66308 15.1301C4.89908 16.0577 4.00948 17.4789 4.51237 18.3438C4.78615 18.8145 5.40732 19.0077 6.02769 19.1935C6.10009 19.2152 6.23702 19.2962 6.14427 19.4314C5.71986 20.1641 2.30388 26.0609 2.30388 26.0609C2.03212 26.5299 2.2528 26.932 2.79412 26.9548L5.44381 27.0653C5.98513 27.0881 6.66419 27.4815 6.95313 27.9399L8.36703 30.184C8.65597 30.6424 9.11444 30.6339 9.38611 30.1649C9.38611 30.1649 13.3422 23.3334 13.3438 23.3314C13.4232 23.2386 13.503 23.2577 13.5411 23.2901C13.974 23.6588 14.5774 24.0265 15.081 24.0265C15.5749 24.0265 16.0323 23.6803 16.4846 23.2943C16.5214 23.2629 16.611 23.1986 16.6765 23.3321C16.6776 23.3342 20.6293 30.1283 20.6293 30.1283C20.9017 30.5967 21.3602 30.6047 21.6483 30.1457L23.0582 27.8989C23.3464 27.4402 24.0247 27.0452 24.566 27.0216L27.2155 26.9061C27.7565 26.8824 27.9765 26.4797 27.7041 26.0114ZM19.3951 19.8663C16.4655 21.5711 12.949 21.3884 10.2631 19.6939C6.32741 17.1723 5.03245 11.9563 7.39838 7.87183C9.79131 3.74027 15.0515 2.28242 19.2194 4.55288C19.2413 4.5648 19.2629 4.57712 19.2846 4.58929C19.3155 4.6064 19.3463 4.62375 19.3771 4.64127C20.6629 5.38276 21.7779 6.45891 22.5763 7.83153C25.0164 12.0268 23.5894 17.4257 19.3951 19.8663Z"
          fill="white"
        />
        <path
          d="M18.7213 5.87433C18.7091 5.86727 18.6968 5.8607 18.6846 5.85373C16.4582 4.56488 13.6205 4.47065 11.2423 5.85462C7.71304 7.90832 6.51212 12.4509 8.56535 15.9809C9.19188 17.0581 10.0504 17.918 11.041 18.5351C11.1252 18.5884 11.2104 18.6409 11.2975 18.6913C14.8305 20.7386 19.3697 19.5292 21.4164 15.9956C23.4631 12.4617 22.2543 7.92162 18.7213 5.87433ZM19.6051 11.6513L18.368 12.8574C17.9849 13.2309 17.7454 13.9681 17.836 14.4956L18.128 16.1986C18.2185 16.7261 17.905 16.9539 17.4314 16.7049L15.9023 15.9008C15.4287 15.6517 14.6537 15.6517 14.1801 15.9008L12.6511 16.7049C12.1774 16.9539 11.8639 16.7261 11.9544 16.1986L12.2464 14.4956C12.3369 13.9681 12.0974 13.2309 11.7143 12.8574L10.4773 11.6513C10.0941 11.2778 10.2139 10.9092 10.7434 10.8321L12.4529 10.5837C12.9823 10.5067 13.6093 10.0511 13.8462 9.57116L14.6107 8.02177C14.8475 7.54187 15.235 7.54187 15.4717 8.02177L16.2363 9.57116C16.4731 10.0511 17.1001 10.5067 17.6296 10.5837L19.3391 10.8321C19.8685 10.9092 19.9883 11.2778 19.6051 11.6513Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3115_1330">
          <rect
            width={30}
            height="30.0067"
            fill="white"
            transform="translate(0 0.515625)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

function Icon3() {
  return (
    <svg
      width={31}
      height={31}
      viewBox="0 0 31 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.0204 1.55977L13.8376 3.66229C13.8621 3.72547 13.9194 3.76608 13.9885 3.76936L16.2879 3.87884C16.4443 3.88629 16.5083 4.07875 16.3863 4.17457L14.5919 5.58348C14.538 5.6258 14.5161 5.69149 14.5343 5.75672L15.1383 7.92688C15.1794 8.07445 15.0118 8.19342 14.8808 8.10961L12.9546 6.87787C12.8967 6.84083 12.826 6.84083 12.7681 6.87787L10.8419 8.10961C10.7109 8.19342 10.5434 8.07445 10.5845 7.92688L11.1884 5.75667C11.2065 5.69144 11.1847 5.62574 11.1308 5.58342L9.3364 4.17451C9.21438 4.07869 9.27839 3.88623 9.43479 3.87878L11.7342 3.76931C11.8033 3.76602 11.8605 3.72541 11.8851 3.66223L12.7023 1.55971C12.7574 1.41759 12.9652 1.41759 13.0204 1.55977ZM24.1381 19.3665C27.0265 19.3665 29.368 21.6539 29.368 24.4755C29.368 27.297 27.0265 29.5844 24.1381 29.5844C21.2498 29.5844 18.9083 27.297 18.9083 24.4755C18.9083 21.6539 21.2498 19.3665 24.1381 19.3665ZM23.4512 24.9261L22.2827 23.7846C21.9798 23.4887 21.4885 23.4887 21.1856 23.7846C20.8828 24.0804 20.8828 24.5603 21.1856 24.8562L22.9044 26.5352C23.2072 26.8311 23.6985 26.8311 24.0014 26.5352C25.0373 25.5232 26.0616 24.4997 27.0928 23.483C27.3935 23.1866 27.3925 22.7082 27.0894 22.4137C26.7863 22.1192 26.2952 22.1201 25.9942 22.4171L23.4512 24.9261ZM4.25441 26.8422L18.4407 26.8421C18.1239 26.1152 17.9485 25.3154 17.9485 24.4755C17.9485 23.1981 18.3543 22.0135 19.0464 21.0372C17.403 19.6059 15.2364 18.7358 12.8614 18.7358C8.28976 18.7358 4.48898 21.9589 3.70942 26.2058C3.64811 26.5394 3.90746 26.8422 4.25441 26.8422ZM12.8614 8.9641C15.458 8.9641 17.5629 11.0204 17.5629 13.5569C17.5629 16.0934 15.458 18.1497 12.8614 18.1497C10.2649 18.1497 8.15999 16.0934 8.15999 13.5569C8.15993 11.0204 10.2649 8.9641 12.8614 8.9641ZM20.9413 4.53583C20.8861 4.39365 20.6783 4.39365 20.623 4.53583L19.8058 6.63835C19.7813 6.70158 19.7241 6.74214 19.6549 6.74542L17.3555 6.8549C17.1991 6.86234 17.1351 7.05481 17.2571 7.15063L19.0515 8.55954C19.1055 8.60191 19.1273 8.66755 19.1091 8.73278L18.5052 10.903C18.4641 11.0506 18.6316 11.1695 18.7627 11.0857L20.6888 9.85399C20.7467 9.81695 20.8174 9.81695 20.8753 9.85399L22.8015 11.0857C22.9325 11.1695 23.1001 11.0506 23.059 10.903L22.455 8.73284C22.4368 8.66761 22.4586 8.60197 22.5126 8.5596L24.307 7.15069C24.429 7.05487 24.365 6.8624 24.2086 6.85496L21.9091 6.74548C21.84 6.7422 21.7829 6.70158 21.7583 6.63841L20.9413 4.53583ZM5.09954 4.53583C5.04428 4.39365 4.83646 4.39365 4.78127 4.53583L3.9641 6.63835C3.93956 6.70158 3.88233 6.74214 3.81321 6.74542L1.51378 6.8549C1.35737 6.86234 1.29336 7.05481 1.41539 7.15063L3.2098 8.55954C3.26373 8.60191 3.28557 8.66755 3.26739 8.73278L2.66344 10.903C2.62234 11.0506 2.7899 11.1695 2.92093 11.0857L4.84708 9.85399C4.90498 9.81695 4.97565 9.81695 5.03354 9.85399L6.95976 11.0857C7.09078 11.1695 7.25835 11.0506 7.21725 10.903L6.61329 8.73284C6.59512 8.66761 6.61695 8.60197 6.67089 8.5596L8.4653 7.15069C8.58732 7.05487 8.52331 6.8624 8.36691 6.85496L6.06747 6.74548C5.99836 6.7422 5.94119 6.70158 5.91659 6.63841L5.09954 4.53583Z"
        fill="white"
      />
    </svg>
  );
}

function Icon4() {
  return (
    <svg
      width={31}
      height={26}
      viewBox="0 0 31 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3115_1355)">
        <path
          d="M11.6968 11.0664C12.1929 11.0664 12.5967 11.4607 12.5967 11.9453C12.5967 12.4299 12.1929 12.8242 11.6968 12.8242C11.2006 12.8242 10.7969 12.4299 10.7969 11.9453C10.7969 11.4607 11.2006 11.0664 11.6968 11.0664Z"
          fill="white"
        />
        <path
          d="M12.6025 7.63479V9.45629C12.3212 9.35841 12.0182 9.30508 11.7026 9.30508C10.2142 9.30508 9.00293 10.4884 9.00293 11.9424C9.00293 13.3964 10.2142 14.5797 11.7026 14.5797C12.0182 14.5797 12.3212 14.5264 12.6025 14.4285V16.25C12.308 16.3086 12.0068 16.3379 11.7026 16.3379C9.22191 16.3379 7.20312 14.3658 7.20312 11.9424C7.20312 9.519 9.22191 7.54688 11.7026 7.54688C12.0068 7.54688 12.308 7.57618 12.6025 7.63479Z"
          fill="white"
        />
        <path
          d="M5.39942 11.944C5.39942 15.3373 8.22512 18.0977 11.6988 18.0977C12.0017 18.0977 12.3023 18.0772 12.5987 18.035V23.3723H9.89894C9.4022 23.3723 8.99904 22.9785 8.99904 22.4932V21.2484C8.26292 21.0439 7.55319 20.7543 6.87707 20.3834L5.97176 21.2683C5.6202 21.6118 5.05086 21.6118 4.6993 21.2683L2.15377 18.7816C1.80209 18.4381 1.80347 17.8808 2.15377 17.5386L3.05967 16.6536C2.67931 15.9937 2.38355 15.3004 2.17417 14.5813H0.899904C0.403157 14.5813 0 14.1874 0 13.7022V10.1858C0 9.7005 0.403157 9.30666 0.899904 9.30666H2.17417C2.38355 8.58755 2.67931 7.89423 3.05967 7.23373L2.15377 6.34935C1.80347 6.00715 1.80209 5.44986 2.15377 5.1063L4.6993 2.61961C5.05086 2.27618 5.6202 2.27618 5.97176 2.61961L6.87767 3.50458C7.55319 3.13301 8.26292 2.84408 8.99904 2.63954V1.39473C8.99904 0.909463 9.4022 0.515625 9.89894 0.515625H12.5987V5.85296C12.3023 5.81076 12.0017 5.79025 11.6988 5.79025C8.22512 5.79025 5.39942 8.55063 5.39942 11.944Z"
          fill="white"
        />
        <path
          d="M25.3173 1.03019V5.79025H30.19L25.3173 1.03019ZM17.0981 5.79025H20.8178C21.3145 5.79025 21.7177 6.18408 21.7177 6.66935C21.7177 7.15461 21.3145 7.54845 20.8178 7.54845H17.0981C16.6014 7.54845 16.1982 7.15461 16.1982 6.66935C16.1982 6.18408 16.6014 5.79025 17.0981 5.79025ZM26.2172 18.0977H17.0981C16.6014 18.0977 16.1982 17.7039 16.1982 17.2186C16.1982 16.7333 16.6014 16.3395 17.0981 16.3395H26.2172C26.7139 16.3395 27.1171 16.7333 27.1171 17.2186C27.1171 17.7039 26.7139 18.0977 26.2172 18.0977ZM26.2172 14.5813H17.0981C16.6014 14.5813 16.1982 14.1874 16.1982 13.7022C16.1982 13.2169 16.6014 12.8231 17.0981 12.8231H26.2172C26.7139 12.8231 27.1171 13.2169 27.1171 13.7022C27.1171 14.1874 26.7139 14.5813 26.2172 14.5813ZM26.2172 11.0649H17.0981C16.6014 11.0649 16.1982 10.671 16.1982 10.1858C16.1982 9.7005 16.6014 9.30666 17.0981 9.30666H26.2172C26.7139 9.30666 27.1171 9.7005 27.1171 10.1858C27.1171 10.671 26.7139 11.0649 26.2172 11.0649ZM24.4174 7.54845C23.9206 7.54845 23.5175 7.15461 23.5175 6.66935V0.515625H14.3984V23.3723H29.8168C30.3135 23.3723 30.7167 22.9785 30.7167 22.4932V7.54845H24.4174Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3115_1355">
          <rect
            width={31}
            height={25}
            fill="white"
            transform="translate(0 0.515625)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export const statisticsSectionData: StatisticsSectionProps = {
  statistics: [
    {
      icon: <Icon1 />,
      value: 200,
      counterSuffix: 'K',
      title: 'Visitor Monthly',
    },
    {
      icon: <Icon2 />,
      value: 50,
      counterSuffix: '%',
      title: 'Growth Monthly',
    },
    {
      icon: <Icon3 />,
      value: 1,
      counterSuffix: 'M+',
      title: 'Happy Clients',
    },
    {
      icon: <Icon4 />,
      value: 30,
      counterSuffix: '+',
      title: 'Country',
    },
  ],
};
