import { FooterSectionProps } from '@/src/layout/footer/v1';
import { FaFacebookF, FaTwitter, FaInstagram } from 'react-icons/fa6';

export const footerSectionData: FooterSectionProps = {
  about: {
    description:
      'Our expert team of developers, designers, and strategists bring a wealth of experience and creativity to every project. We specialize in a wide range of services designed to help your business grow and adapt to the ever-changing technological landscape.',
    socialLinks: [
      // {
      //   icon: <FaFacebookF />,
      //   href: 'https://www.facebook.com/',
      // },
      // {
      //   icon: <FaTwitter />,
      //   href: 'https://twitter.com/',
      // },
      // {
      //   icon: <FaInstagram />,
      //   href: 'https://www.instagram.com/',
      // },
    ],
  },
  columnOne: {
    title: 'Service Links',
    links: [
      {
        label: 'Application Development',
        href: '/services/app-development',
        openNewTab: false,
      },
      {
        label: 'Web Development',
        href: '/services/web-development',
        openNewTab: false,
      },
      {
        label: 'UI / UX Design',
        href: '/services/ux',
        openNewTab: false,
      },
      {
        label: 'Custom AI Solutions',
        href: '/services/custom-ai',
        openNewTab: false,
      },
      {
        label: 'Social Media Marketing',
        href: '/services/social-marketing',
        openNewTab: false,
      },
      {
        label: 'Software Outsourcing',
        href: '/services/software-outsourcing',
        openNewTab: false,
      },
    ],
  },
  columnTwo: {
    title: 'Address',
    location: 'De Specerij 25, Almere, The Netherlands',
    mails: ['<EMAIL>'],
    phoneNumbers: ['+31 (0) 618 235 041'],
  },
  columnThree: {
    title: ' ',
    blogs: [
      // {
      //   image: {
      //     src: '/assets/images/blog/blog-sm-1.jpg',
      //     alt: 'We provide a range of IT solutions',
      //   },
      //   title: 'We provide a range of IT solutions',
      //   date: 'january 11, 2024',
      //   slug: './blog-details',
      // },
      // {
      //   image: {
      //     src: '/assets/images/blog/blog-sm-2.jpg',
      //     alt: 'IT solutions enhance efficiency',
      //   },
      //   title: 'IT solutions enhance efficiency',
      //   date: 'january 11, 2024',
      //   slug: './blog-details',
      // },
    ],
  },
  footerBottom: {
    copyrightText: '© Elusive Studio 2024 | All Rights Reserved',
    links: [
      // {
      //   label: 'Trams & Condition',
      //   href: '/',
      //   openNewTab: false,
      // },
      // {
      //   label: 'Privacy Policy',
      //   href: '/',
      //   openNewTab: false,
      // },
      // {
      //   label: 'Contact Us',
      //   href: '/',
      //   openNewTab: false,
      // },
    ],
  },
};
