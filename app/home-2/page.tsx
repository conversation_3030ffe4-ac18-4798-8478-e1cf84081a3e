import { serviceSectionData } from '@/data/service-section/v2/home-page-2';
import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v2';
import { ProjectSection } from '@/src/sections/project/v2';
import { projectSectionData } from '@/data/project-section/v2/home-page';
import { ServiceSection } from '@/src/sections/service/v2';
import { TeamSection } from '@/src/sections/team/v2';
import { WorkprocessSection } from '@/src/sections/work-process/v1';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Home verion two',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection />
      <ServiceSection className="!pt-0" {...serviceSectionData} />
      {/* <CtaSection /> */}
      {/* <PricingSection /> */}
      {/* <AboutSection /> */}
      <WorkprocessSection />
      <ProjectSection {...projectSectionData} />
      <TeamSection />
      {/* <TestimonialSection /> */}
      {/* <StatisticsSection /> */}
      {/* <BlogSection /> */}
      <Footer className="dark:bg-accent-900" footerTopClassName="dark:!pt-0" />
    </>
  );
}
