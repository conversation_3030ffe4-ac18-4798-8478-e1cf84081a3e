import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { AboutSection } from '@/src/sections/about/v1';
import { HeroSection } from '@/src/sections/hero/v3';
import { StatisticsSection } from '@/src/sections/statistics/v1';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | About',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="About Us"
        breadcrumbItems={[
          {
            label: 'Home',
            href: '/',
          },
          {
            label: 'About',
          },
        ]}
      />
      <AboutSection />
      {/* <CtaSection /> */}
      {/* <TestimonialSection /> */}
      <StatisticsSection />
      {/* <AboutSectionTwo /> */}
      {/* <WorkprocessSection /> */}
      <Footer />
    </>
  );
}
