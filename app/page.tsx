import { projectSectionData } from '@/data/project-section/v1/home-page';
import { serviceSectionData } from '@/data/service-section/v1/home-page';
import { Footer } from '@/src/layout/footer/v1';
import { MainHeader } from '@/src/layout/header';
import { ContactSection } from '@/src/sections/contact/v1';
import { Hero } from '@/src/sections/hero/v1';
import { ProjectSection } from '@/src/sections/project/v1';
import { ServiceSection } from '@/src/sections/service/v1';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Home',
  description: 'Elusive Studio - Beyond the Ordinary',
};

// export const sectionClass: SectionProps = {
//   className: 'section-padding-primary',
// };

export default function Page() {
  return (
    <>
      <MainHeader version="1" />
      <Hero />
      <ServiceSection
        className="section-padding-primary"
        {...serviceSectionData}
      />
      {/* <AboutSection /> */}
      {/* <CtaSection /> */}
      <ProjectSection {...projectSectionData} />
      {/* <StatisticsSection {...sectionClass} /> */}
      {/* <TestimonialSection /> */}
      {/* <PricingSection /> */}
      {/* <TeamSection /> */}
      <ContactSection />
      {/* <BlogSection /> */}
      <Footer />
    </>
  );
}
