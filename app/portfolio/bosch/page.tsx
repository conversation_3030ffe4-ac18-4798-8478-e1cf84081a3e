import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { BoschDetailsSection } from '@/src/sections/project-details/bosch';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Bosch',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Bosch Easy Control"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: 'Bosch Easy Control',
          },
        ]}
      />
      <BoschDetailsSection />
      <Footer />
    </>
  );
}
