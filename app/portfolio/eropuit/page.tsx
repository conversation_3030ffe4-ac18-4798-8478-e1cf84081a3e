import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { EropuitDetailsSection } from '@/src/sections/project-details/eropuit';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Eropuit',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="ANWB - Eropuit"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: 'ANWB - Eropuit',
          },
        ]}
      />
      <EropuitDetailsSection />
      <Footer />
    </>
  );
}
