import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { InvoicePointDetailsSection } from '@/src/sections/project-details/invoicepoint';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Portfolio details',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Invoice Point"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: 'Invoice Point',
          },
        ]}
      />
      <InvoicePointDetailsSection />
      <Footer />
    </>
  );
}
