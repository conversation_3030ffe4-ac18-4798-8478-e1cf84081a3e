import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { INGDetailsSection } from '@/src/sections/project-details/ing';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Eropuit',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="ING Bankieren"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: 'ING Bankieren',
          },
        ]}
      />
      <INGDetailsSection />
      <Footer />
    </>
  );
}
