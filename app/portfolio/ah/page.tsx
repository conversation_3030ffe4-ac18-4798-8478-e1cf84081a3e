import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { AHDetailsSection } from '@/src/sections/project-details/ah';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Albert He<PERSON>jn',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="<PERSON> A<PERSON>"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: '<PERSON>',
          },
        ]}
      />
      <AHDetailsSection />
      <Footer />
    </>
  );
}
