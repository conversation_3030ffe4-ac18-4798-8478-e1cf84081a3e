import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { TNTDetailsSection } from '@/src/sections/project-details/tnt';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | TNT',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="TNT Express - Track & Trace"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: 'TNT Express - Track & Trace',
          },
        ]}
      />
      <TNTDetailsSection />
      <Footer />
    </>
  );
}
