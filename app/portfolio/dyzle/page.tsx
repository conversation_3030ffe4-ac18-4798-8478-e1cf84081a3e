import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { DyzleDetailsSection } from '@/src/sections/project-details/dyzle';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Dyzle',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Dyzle"
        breadcrumbItems={[
          {
            label: 'Portfolio',
            href: '/portfolio',
          },
          {
            label: 'Dyzle',
          },
        ]}
      />
      <DyzleDetailsSection />
      <Footer />
    </>
  );
}
