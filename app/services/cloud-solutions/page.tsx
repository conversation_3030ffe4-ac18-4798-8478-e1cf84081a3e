import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { CloudSolutionsServiceDetailsSection } from '@/src/sections/service-details/cloud-solutions';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Cloud Solutions',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Cloud Solutions"
        breadcrumbItems={[
          {
            label: 'Services',
            href: '/services',
          },
          {
            label: 'Cloud Solutions',
          },
        ]}
      />
      <CloudSolutionsServiceDetailsSection />
      <Footer />
    </>
  );
}
