import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { SocialMarketingServiceDetailsSection } from '@/src/sections/service-details/social-marketing';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Social Media Marketing',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Social Media Marketing"
        breadcrumbItems={[
          {
            label: 'Services',
            href: '/services',
          },
          {
            label: 'Social Media Marketing',
          },
        ]}
      />
      <SocialMarketingServiceDetailsSection />
      <Footer />
    </>
  );
}
