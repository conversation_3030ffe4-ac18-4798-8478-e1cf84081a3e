import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { UXServiceDetailsSection } from '@/src/sections/service-details/ux';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | UI / UX Design',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="UI / UX Design"
        breadcrumbItems={[
          {
            label: 'Services',
            href: '/services',
          },
          {
            label: 'UI / UX Design',
          },
        ]}
      />
      <UXServiceDetailsSection />
      <Footer />
    </>
  );
}
