import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { WebDevelopmentServiceDetailsSection } from '@/src/sections/service-details/web-development';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Web Development',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Web Development"
        breadcrumbItems={[
          {
            label: 'Services',
            href: '/services',
          },
          {
            label: 'Web Development',
          },
        ]}
      />
      <WebDevelopmentServiceDetailsSection />
      <Footer />
    </>
  );
}
