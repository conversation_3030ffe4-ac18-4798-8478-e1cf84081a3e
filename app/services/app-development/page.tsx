import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { AppDevelopmentServiceDetailsSection } from '@/src/sections/service-details/app-development';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | App Development',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="App Development"
        breadcrumbItems={[
          {
            label: 'Services',
            href: '/services',
          },
          {
            label: 'App Development',
          },
        ]}
      />
      <AppDevelopmentServiceDetailsSection />
      <Footer />
    </>
  );
}
