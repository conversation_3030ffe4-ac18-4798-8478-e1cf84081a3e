import { Footer } from '@/src/layout/footer/v2';
import { MainHeader } from '@/src/layout/header';
import { HeroSection } from '@/src/sections/hero/v3';
import { CustomAIServiceDetailsSection } from '@/src/sections/service-details/custom-ai';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Elusive Studio | Service details',
  description: 'Elusive Studio - Beyond the Ordinary',
};

export default function Page() {
  return (
    <>
      <MainHeader version="2" />
      <HeroSection
        title="Custom AI Solutions"
        breadcrumbItems={[
          {
            label: 'Services',
            href: '/services',
          },
          {
            label: 'Custom AI Solutions',
          },
        ]}
      />
      <CustomAIServiceDetailsSection />
      <Footer />
    </>
  );
}
