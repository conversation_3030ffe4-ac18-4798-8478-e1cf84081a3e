{"name": "@babel/types", "version": "7.16.7", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "dependencies": {"@babel/helper-validator-identifier": "^7.16.7", "to-fast-properties": "^2.0.0"}, "devDependencies": {"@babel/generator": "^7.16.7", "@babel/parser": "^7.16.7", "chalk": "^4.1.0", "glob": "^7.1.7"}, "engines": {"node": ">=6.9.0"}}