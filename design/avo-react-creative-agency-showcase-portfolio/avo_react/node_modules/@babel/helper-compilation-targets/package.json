{"name": "@babel/helper-compilation-targets", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Helper functions on Babel compilation targets", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-compilation-targets"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/compat-data": "^7.16.4", "@babel/helper-validator-option": "^7.16.7", "browserslist": "^4.17.5", "semver": "^6.3.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@types/semver": "^5.5.0"}, "engines": {"node": ">=6.9.0"}}