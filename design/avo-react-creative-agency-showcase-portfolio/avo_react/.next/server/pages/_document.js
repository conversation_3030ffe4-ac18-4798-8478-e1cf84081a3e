/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.isEqualNode = isEqualNode;\nexports[\"default\"] = initHeadManager;\nexports.DOMAttributeNames = void 0;\nconst DOMAttributeNames = {\n    acceptCharset: 'accept-charset',\n    className: 'class',\n    htmlFor: 'for',\n    httpEquiv: 'http-equiv',\n    noModule: 'noModule'\n};\nexports.DOMAttributeNames = DOMAttributeNames;\nfunction reactElementToDOM({ type , props  }) {\n    const el = document.createElement(type);\n    for(const p in props){\n        if (!props.hasOwnProperty(p)) continue;\n        if (p === 'children' || p === 'dangerouslySetInnerHTML') continue;\n        // we don't render undefined props to the DOM\n        if (props[p] === undefined) continue;\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (type === 'script' && (attr === 'async' || attr === 'defer' || attr === 'noModule')) {\n            el[attr] = !!props[p];\n        } else {\n            el.setAttribute(attr, props[p]);\n        }\n    }\n    const { children , dangerouslySetInnerHTML  } = props;\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n    }\n    return el;\n}\nfunction isEqualNode(oldTag, newTag) {\n    if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n        const nonce = newTag.getAttribute('nonce');\n        // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n        // be stripped if there is no content security policy response header that includes a nonce.\n        if (nonce && !oldTag.getAttribute('nonce')) {\n            const cloneTag = newTag.cloneNode(true);\n            cloneTag.setAttribute('nonce', '');\n            cloneTag.nonce = nonce;\n            return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag);\n        }\n    }\n    return oldTag.isEqualNode(newTag);\n}\nfunction updateElements(type, components) {\n    const headEl = document.getElementsByTagName('head')[0];\n    const headCountEl = headEl.querySelector('meta[name=next-head-count]');\n    if (true) {\n        if (!headCountEl) {\n            console.error('Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing');\n            return;\n        }\n    }\n    const headCount = Number(headCountEl.content);\n    const oldTags = [];\n    for(let i = 0, j = headCountEl.previousElementSibling; i < headCount; i++, j = (j === null || j === void 0 ? void 0 : j.previousElementSibling) || null){\n        var ref;\n        if ((j === null || j === void 0 ? void 0 : (ref = j.tagName) === null || ref === void 0 ? void 0 : ref.toLowerCase()) === type) {\n            oldTags.push(j);\n        }\n    }\n    const newTags = components.map(reactElementToDOM).filter((newTag)=>{\n        for(let k = 0, len = oldTags.length; k < len; k++){\n            const oldTag = oldTags[k];\n            if (isEqualNode(oldTag, newTag)) {\n                oldTags.splice(k, 1);\n                return false;\n            }\n        }\n        return true;\n    });\n    oldTags.forEach((t)=>{\n        var ref;\n        return (ref = t.parentNode) === null || ref === void 0 ? void 0 : ref.removeChild(t);\n    });\n    newTags.forEach((t)=>headEl.insertBefore(t, headCountEl)\n    );\n    headCountEl.content = (headCount - oldTags.length + newTags.length).toString();\n}\nfunction initHeadManager() {\n    let updatePromise = null;\n    return {\n        mountedInstances: new Set(),\n        updateHead: (head)=>{\n            const promise = updatePromise = Promise.resolve().then(()=>{\n                if (promise !== updatePromise) return;\n                updatePromise = null;\n                const tags = {\n                };\n                head.forEach((h)=>{\n                    if (// it won't be inlined. In this case revert to the original behavior\n                    h.type === 'link' && h.props['data-optimized-fonts']) {\n                        if (document.querySelector(`style[data-href=\"${h.props['data-href']}\"]`)) {\n                            return;\n                        } else {\n                            h.props.href = h.props['data-href'];\n                            h.props['data-href'] = undefined;\n                        }\n                    }\n                    const components = tags[h.type] || [];\n                    components.push(h);\n                    tags[h.type] = components;\n                });\n                const titleComponent = tags.title ? tags.title[0] : null;\n                let title = '';\n                if (titleComponent) {\n                    const { children  } = titleComponent.props;\n                    title = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n                }\n                if (title !== document.title) document.title = title;\n                [\n                    'meta',\n                    'base',\n                    'link',\n                    'style',\n                    'script'\n                ].forEach((type)=>{\n                    updateElements(type, tags[type] || []);\n                });\n            });\n        }\n    };\n} //# sourceMappingURL=head-manager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/head-manager.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.cancelIdleCallback = exports.requestIdleCallback = void 0;\nconst requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nexports.requestIdleCallback = requestIdleCallback;\nconst cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nexports.cancelIdleCallback = cancelIdleCallback; //# sourceMappingURL=request-idle-callback.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/request-idle-callback.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.initScriptLoader = initScriptLoader;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _headManagerContext = __webpack_require__(/*! ../shared/lib/head-manager-context */ \"../shared/lib/head-manager-context\");\nvar _headManager = __webpack_require__(/*! ./head-manager */ \"./node_modules/next/dist/client/head-manager.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    } else {\n        var newObj = {\n        };\n        if (obj != null) {\n            for(var key in obj){\n                if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {\n                    };\n                    if (desc.get || desc.set) {\n                        Object.defineProperty(newObj, key, desc);\n                    } else {\n                        newObj[key] = obj[key];\n                    }\n                }\n            }\n        }\n        newObj.default = obj;\n        return newObj;\n    }\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {\n        };\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === \"function\") {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {\n    };\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {\n    };\n    var target = {\n    };\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst ignoreProps = [\n    'onLoad',\n    'dangerouslySetInnerHTML',\n    'children',\n    'onError',\n    'strategy', \n];\nconst loadScript = (props)=>{\n    const { src , id , onLoad =()=>{\n    } , dangerouslySetInnerHTML , children ='' , strategy ='afterInteractive' , onError ,  } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // Execute onLoad since the script loading has begun\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    const el = document.createElement('script');\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener('load', function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n        });\n        el.addEventListener('error', function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (src) {\n        ScriptCache.set(src, loadPromise);\n    }\n    LoadCache.add(cacheKey);\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n    } else if (src) {\n        el.src = src;\n    }\n    for (const [k, value] of Object.entries(props)){\n        if (value === undefined || ignoreProps.includes(k)) {\n            continue;\n        }\n        const attr = _headManager.DOMAttributeNames[k] || k.toLowerCase();\n        el.setAttribute(attr, value);\n    }\n    el.setAttribute('data-nscript', strategy);\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy ='afterInteractive'  } = props;\n    if (strategy === 'afterInteractive') {\n        loadScript(props);\n    } else if (strategy === 'lazyOnload') {\n        window.addEventListener('load', ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props)\n            );\n        });\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === 'complete') {\n        (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props)\n        );\n    } else {\n        window.addEventListener('load', ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props)\n            );\n        });\n    }\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n}\nfunction Script(props) {\n    const { src ='' , onLoad =()=>{\n    } , dangerouslySetInnerHTML , strategy ='afterInteractive' , onError  } = props, restProps = _objectWithoutProperties(props, [\n        \"src\",\n        \"onLoad\",\n        \"dangerouslySetInnerHTML\",\n        \"strategy\",\n        \"onError\"\n    ]);\n    // Context is available only during SSR\n    const { updateScripts , scripts , getIsSsr  } = (0, _react).useContext(_headManagerContext.HeadManagerContext);\n    (0, _react).useEffect(()=>{\n        if (strategy === 'afterInteractive') {\n            loadScript(props);\n        } else if (strategy === 'lazyOnload') {\n            loadLazyScript(props);\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === 'beforeInteractive') {\n        if (updateScripts) {\n            scripts.beforeInteractive = (scripts.beforeInteractive || []).concat([\n                _objectSpread({\n                    src,\n                    onLoad,\n                    onError\n                }, restProps), \n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(restProps.id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript(props);\n        }\n    }\n    return null;\n}\nvar _default = Script;\nexports[\"default\"] = _default; //# sourceMappingURL=script.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/script.js\n");

/***/ }),

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DocumentContext\", ({\n    enumerable: true,\n    get: function() {\n        return _utils.DocumentContext;\n    }\n}));\nObject.defineProperty(exports, \"DocumentInitialProps\", ({\n    enumerable: true,\n    get: function() {\n        return _utils.DocumentInitialProps;\n    }\n}));\nObject.defineProperty(exports, \"DocumentProps\", ({\n    enumerable: true,\n    get: function() {\n        return _utils.DocumentProps;\n    }\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _utils = __webpack_require__(/*! ../shared/lib/utils */ \"../shared/lib/utils\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _utils1 = __webpack_require__(/*! ../server/utils */ \"../server/utils\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _script = _interopRequireDefault(__webpack_require__(/*! ../client/script */ \"./node_modules/next/dist/client/script.js\"));\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    } else {\n        var newObj = {\n        };\n        if (obj != null) {\n            for(var key in obj){\n                if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {\n                    };\n                    if (desc.get || desc.set) {\n                        Object.defineProperty(newObj, key, desc);\n                    } else {\n                        newObj[key] = obj[key];\n                    }\n                }\n            }\n        }\n        newObj.default = obj;\n        return newObj;\n    }\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, '/_app');\n    const pageFiles = inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n    ).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        })\n    );\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    return (scriptLoader.beforeInteractive || []).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        return(/*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({\n        }, scriptProps, {\n            key: scriptProps.src || index,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        })));\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith('.js') || files.allFiles.includes(file)) return null;\n        return(/*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith('.js')\n    );\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) === null || ref === void 0 ? void 0 : ref.filter((file)=>file.endsWith('.js')\n    );\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return(/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n}\nclass Document extends _react.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return(/*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null))));\n    }\n}\nexports[\"default\"] = Document;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale  } = (0, _react).useContext(_utils.HtmlContext);\n    docComponentsRendered.Html = true;\n    return(/*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({\n    }, props, {\n        lang: props.lang || locale || undefined,\n        amp: inAmpMode ? '' : undefined,\n        \"data-ampdevmode\": inAmpMode && \"development\" !== 'production' ? '' : undefined\n    })));\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el === null || el === void 0 ? void 0 : (ref = el.props) === null || ref === void 0 ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) === null || ref1 === void 0 ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el)\n                );\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return(/*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html\n            ).join('').replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, '').replace(/\\/\\*@ sourceURL=.*?\\*\\//g, '')\n        }\n    }));\n}\nclass Head extends _react.Component {\n    getCssLinks(files6) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts ,  } = this.context;\n        const cssFiles = files6.allFiles.filter((f)=>f.endsWith('.css')\n        );\n        const sharedFiles = new Set(files6.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith('.css')\n        )));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f))\n            );\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? '' : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : ''\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin ,  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith('.js')) {\n                return null;\n            }\n            return(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            }));\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files1) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin ,  } = this.context;\n        const preloadFiles = files1.allFiles.filter((file)=>{\n            return file.endsWith('.js');\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })\n            ),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })\n            ), \n        ];\n    }\n    getDynamicChunks(files2) {\n        return getDynamicChunks(this.context, this.props, files2);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files3) {\n        return getScripts(this.context, this.props, files3);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    handleDocumentScriptLoaderItems(children1) {\n        const { scriptLoader  } = this.context;\n        const scriptLoaderItems = [];\n        const filteredChildren = [];\n        _react.default.Children.forEach(children1, (child)=>{\n            if (child.type === _script.default) {\n                if (child.props.strategy === 'beforeInteractive') {\n                    scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                        {\n                            ...child.props\n                        }, \n                    ]);\n                    return;\n                } else if ([\n                    'lazyOnload',\n                    'afterInteractive'\n                ].includes(child.props.strategy)) {\n                    scriptLoaderItems.push(child.props);\n                    return;\n                }\n            }\n            filteredChildren.push(child);\n        });\n        this.context.__NEXT_DATA__.scriptLoader = scriptLoaderItems;\n        return filteredChildren;\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            if (c.type === 'link' && c.props['href'] && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>c.props['href'].startsWith(url)\n            )) {\n                const newProps = {\n                    ...c.props || {\n                    }\n                };\n                newProps['data-href'] = newProps['href'];\n                newProps['href'] = undefined;\n                return(/*#__PURE__*/ _react.default.cloneElement(c, newProps));\n            } else if (c.props && c.props['children']) {\n                c.props['children'] = this.makeStylesheetInert(c.props['children']);\n            }\n            return c;\n        });\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , useMaybeDeferContent , optimizeCss , optimizeFonts , optimizeImages , concurrentFeatures ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === 'link' && c.props['rel'] === 'preload' && c.props['as'] === 'style') {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child === null || child === void 0 ? void 0 : (ref = child.props) === null || ref === void 0 ? void 0 : ref['data-react-helmet'];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child === null || child === void 0 ? void 0 : child.type) === 'title') {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child === null || child === void 0 ? void 0 : child.type) === 'meta' && (child === null || child === void 0 ? void 0 : (ref6 = child.props) === null || ref6 === void 0 ? void 0 : ref6.name) === 'viewport') {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn('Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        if (false) {}\n        children = this.handleDocumentScriptLoaderItems(children);\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if (inAmpMode) {\n                let badProp = '';\n                if (type === 'meta' && props.name === 'viewport') {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === 'link' && props.rel === 'canonical') {\n                    hasCanonicalRel = true;\n                } else if (type === 'script') {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf('ampproject') < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === 'text/javascript')) {\n                        badProp = '<script';\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += '/>';\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === 'link' && props.rel === 'amphtml') {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page, inAmpMode);\n        // Must use nested component to allow use of a custom hook\n        const DeferrableHead = ()=>{\n            const getDynamicHeadContent = ()=>{\n                return(/*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                    name: \"next-head-count\",\n                    content: _react.default.Children.count(head || []).toString()\n                })));\n            };\n            const getDynamicScriptPreloads = ()=>{\n                return(/*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files)));\n            };\n            const getDynamicScriptContent = ()=>{\n                return(/*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)));\n            };\n            const [isDeferred] = useMaybeDeferContent('HEAD', ()=>{\n                return(/*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, getDynamicHeadContent(), getDynamicScriptPreloads(), getDynamicScriptContent()));\n            });\n            var _nonce, _nonce1;\n            return(/*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({\n            }, this.props), !concurrentFeatures && this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n                \"data-next-hide-fouc\": true,\n                \"data-ampdevmode\": inAmpMode ? 'true' : undefined,\n                dangerouslySetInnerHTML: {\n                    __html: `body{display:none}`\n                }\n            }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n                \"data-next-hide-fouc\": true,\n                \"data-ampdevmode\": inAmpMode ? 'true' : undefined\n            }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `body{display:block}`\n                }\n            }))), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                name: \"next-font-preconnect\"\n            }), !isDeferred && getDynamicHeadContent(), inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n            }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"canonical\",\n                href: canonicalBase + (0, _utils1).cleanAmpPath(dangerousAsPath)\n            }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                as: \"script\",\n                href: \"https://cdn.ampproject.org/v0.js\"\n            }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n                styles: styles\n            }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n                \"amp-boilerplate\": \"\",\n                dangerouslySetInnerHTML: {\n                    __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n                }\n            }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n                \"amp-boilerplate\": \"\",\n                dangerouslySetInnerHTML: {\n                    __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n                }\n            })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n                async: true,\n                src: \"https://cdn.ampproject.org/v0.js\"\n            })), !inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"amphtml\",\n                href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n            }), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n                \"data-n-css\": (_nonce = this.props.nonce) !== null && _nonce !== void 0 ? _nonce : ''\n            }), optimizeImages && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                name: \"next-image-preload\"\n            }), !isDeferred && getDynamicScriptPreloads(), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !isDeferred && getDynamicScriptContent(), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n                \"data-n-css\": (_nonce1 = this.props.nonce) !== null && _nonce1 !== void 0 ? _nonce1 : ''\n            }), this.context.isDevelopment && // ordering matches production\n            // (by default, style-loader injects at the bottom of <head />)\n            /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n                id: \"__next_css__DO_NOT_USE__\"\n            }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {\n            }, ...headTags || [])));\n        };\n        return(/*#__PURE__*/ _react.default.createElement(DeferrableHead, null));\n    }\n}\nexports.Head = Head;\nHead.contextType = _utils.HtmlContext;\nfunction Main({ children  }) {\n    const { docComponentsRendered , useMainContent  } = (0, _react).useContext(_utils.HtmlContext);\n    const content = useMainContent(children);\n    docComponentsRendered.Main = true;\n    return content;\n}\nclass NextScript extends _react.Component {\n    getDynamicChunks(files4) {\n        return getDynamicChunks(this.context, this.props, files4);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files5) {\n        return getScripts(this.context, this.props, files5);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (true) {\n                const bytes = Buffer.from(data).byteLength;\n                const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n                if (bytes > 128 * 1000) {\n                    console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\" is ${prettyBytes(bytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n                }\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf('circular structure')) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , useMaybeDeferContent , crossOrigin ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        // Must nest component to use custom hook\n        const DeferrableNextScript = ()=>{\n            const [, content] = useMaybeDeferContent('NEXT_SCRIPT', ()=>{\n                if (inAmpMode) {\n                    const ampDevFiles = [\n                        ...buildManifest.devFiles,\n                        ...buildManifest.polyfillFiles,\n                        ...buildManifest.ampDevFiles, \n                    ];\n                    return(/*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                        id: \"__NEXT_DATA__\",\n                        type: \"application/json\",\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin,\n                        dangerouslySetInnerHTML: {\n                            __html: NextScript.getInlineScriptSource(this.context)\n                        },\n                        \"data-ampdevmode\": true\n                    }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                            key: file,\n                            src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                            nonce: this.props.nonce,\n                            crossOrigin: this.props.crossOrigin || crossOrigin,\n                            \"data-ampdevmode\": true\n                        })\n                    )));\n                }\n                if (true) {\n                    if (this.props.crossOrigin) console.warn('Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n                }\n                const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page, inAmpMode);\n                return(/*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                        key: file,\n                        src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin\n                    })\n                ) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                    id: \"__NEXT_DATA__\",\n                    type: \"application/json\",\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    dangerouslySetInnerHTML: {\n                        __html: NextScript.getInlineScriptSource(this.context)\n                    }\n                }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)));\n            });\n            if (inAmpMode && \"development\" === 'production') {}\n            return content;\n        };\n        return(/*#__PURE__*/ _react.default.createElement(DeferrableNextScript, null));\n    }\n}\nexports.NextScript = NextScript;\nNextScript.contextType = _utils.HtmlContext;\nNextScript.safariNomoduleFix = '!function(){var e=document,t=e.createElement(\"script\");if(!(\"noModule\"in t)&&\"onbeforeload\"in t){var n=!1;e.addEventListener(\"beforeload\",function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute(\"nomodule\")||!n)return;e.preventDefault()},!0),t.type=\"module\",t.src=\".\",e.head.appendChild(t),t.remove()}}();';\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`;\n} //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./src/pages/_document.js":
/*!********************************!*\
  !*** ./src/pages/_document.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    static async getInitialProps(ctx) {\n        const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n        return {\n            ...initialProps\n        };\n    }\n    render() {\n        return(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            lang: \"en\",\n            __source: {\n                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                lineNumber: 13,\n                columnNumber: 7\n            },\n            __self: this,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    __source: {\n                        fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    },\n                    __self: this,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"meta\", {\n                            httpEquiv: \"X-UA-Compatible\",\n                            content: \"IE=edge\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"meta\", {\n                            name: \"keywords\",\n                            content: \"HTML5 Template Avo onepage themeforest\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"meta\", {\n                            name: \"description\",\n                            content: \"Avo - Onepage Multi-Purpose HTML5 Template\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"meta\", {\n                            name: \"author\",\n                            content: \"\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"link\", {\n                            rel: \"shortcut icon\",\n                            href: \"/img/favicon.ico\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900&display=swap\",\n                            rel: \"stylesheet\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Barlow:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                            rel: \"stylesheet\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css?family=Playfair+Display:400,500,600,700,800,900&display=swap\",\n                            rel: \"stylesheet\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;500;600;700&display=swap\",\n                            rel: \"stylesheet\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Barlow+Condensed:wght@200;300;400;500;600;700&display=swap\",\n                            rel: \"stylesheet\",\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"body\", {\n                    __source: {\n                        fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    },\n                    __self: this,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {\n                            __source: {\n                                fileName: \"/Users/<USER>/Documents/Projects/elusivestudio-website/design/avo-react-creative-agency-showcase-portfolio/avo_react/src/pages/_document.js\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            },\n                            __self: this\n                        })\n                    ]\n                })\n            ]\n        }));\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyDocument);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nfunction isError(err) {\n    return typeof err === 'object' && err !== null && 'name' in err && 'message' in err;\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi9pcy1lcnJvci5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFlO0FBQ2Y7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXZvMi1yZWFjdC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbGliL2lzLWVycm9yLmpzPzE3OGUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBpc0Vycm9yO1xuZnVuY3Rpb24gaXNFcnJvcihlcnIpIHtcbiAgICByZXR1cm4gdHlwZW9mIGVyciA9PT0gJ29iamVjdCcgJiYgZXJyICE9PSBudWxsICYmICduYW1lJyBpbiBlcnIgJiYgJ21lc3NhZ2UnIGluIGVycjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXMtZXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    'B',\n    'kB',\n    'MB',\n    'GB',\n    'TB',\n    'PB',\n    'EB',\n    'ZB',\n    'YB'\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === 'string') {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({\n    }, options);\n    if (options.signed && number === 0) {\n        return ' 0 B';\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? '-' : options.signed ? '+' : '';\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + ' B';\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + ' ' + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/document.js":
/*!***************************************!*\
  !*** ./node_modules/next/document.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSEFBa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdm8yLXJlYWN0Ly4vbm9kZV9tb2R1bGVzL25leHQvZG9jdW1lbnQuanM/OWExNCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9wYWdlcy9fZG9jdW1lbnQnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/document.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/head-manager-context":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/head-manager-context.js" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head-manager-context.js");

/***/ }),

/***/ "../shared/lib/utils":
/*!************************************************!*\
  !*** external "next/dist/shared/lib/utils.js" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./src/pages/_document.js"));
module.exports = __webpack_exports__;

})();